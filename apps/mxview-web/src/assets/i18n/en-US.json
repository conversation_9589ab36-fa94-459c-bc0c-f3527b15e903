{"ABOUT": {"debug_log": "Debug Logs", "debug_log_desc": "Click \"Download\" to generate and download the debug logs. If you are experiencing issues, send the downloaded log file through the Moxa support channel for further analysis.", "desc": "Copyright Moxa, Inc. All Rights Reserved.", "eula": "View the End User License Agreement (EULA)", "gateway": "Gateway Version", "title": "About", "web": "Web Version"}, "ACCOUNT_MANAGEMENT": {"access_site": "Accessible Sites", "add_account_dialog": "Add User Account", "add_user_fail": "Failed to create the new account", "add_user_success": "New account created successfully", "admin": "Supervisor", "all_user": "All Users", "authority": "Authority", "change_password": "Change Password", "delete_user_fail": "Failed to delete user account", "delete_user_success": "User account deleted successfully", "demo_user": "Demo User", "filter_account": "Type to filter user accounts", "modify_account_dialog": "Modify User Account", "new_password": "New Password", "old_password": "Old Password", "password": "Password", "password_policy_mismatch": "The password does not conform to the password policy", "superuser": "Administrator", "title": "Account Management", "ui_profile": "UI Profile", "update_user_fail": "Failed to update user account", "update_user_success": "User account updated successfully", "user": "User", "user_account": "User Account", "user_exist": "User already exists", "username": "Username"}, "account_password": {"1st_email": "1st Email Recipient", "2nd_email": "2nd Email Recipient", "account_audit": "Account <PERSON>", "account_info": "Account Information", "account_info_content": "Click the 'Refresh' button to retrieve the account information for all devices. This may take some time.", "account_management": "Account Management", "account_password_management_automation": "Account and Password Management Automation", "account_status_audit": "Accounts Audit", "account_status_baseline": "Accounts Baseline", "account_status_baseline_content": "This operation will create a new baseline and will overwrite the existing one.", "accounts": "Accounts", "activate": "Activate", "add_account": "Add Account", "add_temporary_account": "Add Temporary Account", "added_account": "Added Accounts", "admin": "Admin", "apply_accounts": "Applicable Accounts", "audit_automation": "Audit Automation", "authority": "Authority", "baseline_account": "Baseline Accounts", "baseline_auto_check_failed": "Failed to create the baseline", "change_admin_name": "Change Default \"Admin\" Name", "change_admin_name_content": "MXview One will use the updated account credentials to access the following device(s). Other devices are not affected.", "change_admin_name_contents": "MXview One will use the updated account credentials to access the following device(s). Other devices are not affected.", "check_default_account_failed": "Failed to check the default account.", "check_password_length": "Make the password length is within the maximum allowed password length on the device(s). ", "compability": "Compatible", "create_baseline_failed": "Failed to create the baseline", "create_baseline_failed_no_devices": "Failed to create the baseline. No devices detected. ", "days": "Days", "default_account": "Default Username/Password", "default_password_audit": "Default Password Audit", "default_password_audit_info": "Scanning for default account credentials may take some time and will leave the interface temporarily unavailable. Are you sure you want to continue?", "delete_account": "Delete Account", "delete_temporary_account": "Delete Temporary Account", "delete_temporary_account_info": "Are you sure you want to delete this temporary account?", "delete_temporary_accounts_info": "Are you sure you want to delete these temporary accounts?", "deleted_account": "Deleted Accounts", "device_alias": "<PERSON><PERSON>", "device_ip": "Device IP", "edit_account": "Edit Account", "edit_temporary_account": "Edit Temporary Account", "email_server_configuration": "Email Server Configuration", "email_server_hint": "If you have not received the email with the verification code, check your spam folder or verify the", "email_verified": "The email address has been verified.", "end_date": "<PERSON>id <PERSON>", "end_time": "End Time", "fatiled_to_audit_account_due": "Failed to complete account audit. Unable to retrieve device account information from {{ ip }}.", "fatiled_to_create_baseline_due": "Failed to create the baseline. Unable to retrieve device account information from {{ ip }}.", "get_baseline_failed": "Failed to get the baseline", "get_device_account_failed": "Failed to query device account information. Other requests are in progress. Try again later.", "incorrect_verification_code": "Incorrect verification code.", "last_audit_time": "Last Audited", "last_execution_time": "Last Executed", "max_char": "Maximum {{num}} characters", "model": "Model", "mxa_char": "Maximum {{num}} characters", "new_password": "New Password", "new_username": "New Username", "next_audit_time": "Next Audit", "next_schdeule_start_time": "Next Scheduled for", "no_data": "N/A", "not_activate": "Inactivate", "not_started": "Pending", "not_started_hint": "This task was not executed due to a system shutdown. Click the “Regenerate Password” button to manually execute the task.", "now": "Immediate", "operation": "Action", "password": "Password", "password_automation": "Password Automation", "password_automation_schedule": "Password Automation Schedule", "password_automation_settings": "Password Automation Wizard", "password_email_receiver": "Password Email Recipient", "password_regenerated_info": "MXview One will use the following settings to generate a new password for each device.", "password_resend_info": "Are you sure you want to resend the device password to the following recipient(s)?", "password_resend_result_info": "MXview One has sent the device account and password file to the following email address(es):  ", "password_strength": "Password Strength", "random_password_complexity": "Set Password Complexity", "random_password_length": "Random Password Length", "random_password_length_info": "MXview One will generate the random password for the selected devices.", "randomized_password_failed": "Failed (The device account doesn't match the database account.) ", "refresh_hint": "Please press the 'Refresh' button to retrieve the device accounts before proceeding with this action.", "regenerate_password": "Regenerate Password", "resend_password_email": "Resend Password Email", "retrieve_data": "Retrieving data", "retry_failed_devices": "Retry Failed Devices", "schedule": "Scheduled", "schedule_interval": "Interval", "script_error": "This field may not contain any of the following characters: #%&*{}|:\\\"<>?/\\\\", "select_device": "Select Devices", "select_device_random_password": "Select the device(s) to generate a randomized password for.", "send_password_email": "Send Password Email", "send_password_email_success": "MXview One has sent the device account, password, and execution result to the following email recipient(s):", "set_password_to_device": "Apply Password to <PERSON><PERSON>", "set_schedule_interval_failed": "Failed to set the schedule interval.", "set_schedule_interval_success": "Schedule interval set successfully.", "start_date": "Active From", "start_over": "Start Over", "start_time": "Start Time", "start_wizard": "Start Wizard", "status": {"cli_session_timeout": "CLI Session Timeout", "failed": "Failed", "failed_account_exist": "Failed (This account already exists)", "failed_account_password_incorrect": "Failed (Incorrect account or password)", "failed_limit_reached": "Failed (Device account limit reached)", "failed_not_support_role": "Failed (<PERSON><PERSON> not supported)", "failed_other_request": "Failed (Other requests are in progress)", "failed_retrieve_account_info": "Failed (Unable to retrieve account information)", "finished": "Finished", "in_progress": "In progress ...", "waiting": "Waiting"}, "supervisor": "Supervisor", "temporary_account": "Temporary Accounts", "test_eamil_recipient": "Test Email Recipient", "title": "Account and Password", "unable_to_get_accounts": "Unable to retrieve accounts", "user": "User", "username": "Username", "verififcation_code": "Verification Code", "verift_title": "Verify Your MXview One Account", "verify_code_expiration": "Verification code expires in", "verify_email_not_allowed": "Please wait at least one minute before sending another email.", "verify_email_password_receiver": "Verify Account and Password Email Recipient(s)", "verify_email_receiver": "Veri<PERSON> <PERSON><PERSON>nt", "verify_email_server_failed": "The email server configuration is invalid. Unable to send emails.", "verify_user_failed": "Invalid username or password"}, "ADD_DEVICE": {"add_device_fail": "Failed to add device", "add_device_fail_error_message": {"device_has_existed": "A device with this IP already exists", "license_limitation_reached": "License limit reached", "model_not_exist": "The model does not exist."}, "add_device_success": "Device added successfully", "assign_group": "Assign to Group", "assign_model": "Assign Model", "authentication": "Authentication", "auto_detect_model": "Auto Detect", "data_encryption": "Data Encryption", "encryption_password": "Encryption Password", "encryption_type": "Encryption Protocol", "field_required": "This field is required", "snmp_setting": "SNMP Settings", "snmp_version": "SNMP Version", "title": "Add <PERSON>"}, "ADD_LINK": {"alias": "<PERSON><PERSON>", "device": "<PERSON><PERSON>", "fail": "Failed to add link", "from": "From", "ip_address": "IP Address", "model": "Model", "only_number": "Allows only numbers.", "port": "Port", "success": "Link added successfully", "title": "Add Link", "to": "To"}, "API_MANAGEMENT": {"access_count": "Access Count", "add_failed": "Failed to add API key", "add_success": "API key added successfully", "add_title": "Add New Token", "api_key": "API Key", "application_name": "Application Name", "create_time": "Create Time", "delete_failed": "Failed to delete API key", "delete_success": "API key deleted successfully", "edit_title": "Edit token", "filter": "Type to filter API keys", "regenerate_api_key": "Regenerate the API Key", "regenerate_failed": "Failed to regenerate API key", "regenerate_success": "API key regenerated successfully", "title": "API Key Management", "update_failed": "Failed to update API key", "update_success": "API key updated successfully"}, "ASSIGN_MODEL": {"apply_to_all": "Apply this icon to all devices with the same model", "assign_model_fail": "Failed to assign model", "assign_model_success": "Device model assigned successfully", "ip_address": "IP Address", "model": "Model", "model_icon": "Model Icon", "select_model": "Select Model"}, "AVAILABILITY_REPORT": {"alias": "<PERSON><PERSON>", "average": "Average Availability", "days": "Days", "end_date": "End Date", "filter": "Type to filter availability reports", "from_date": "Start Date", "query_date": "Query Date", "report_generate_day": "Report Generation Date: ", "site_name": "Site Name", "title": "Availability Report", "worst": "Worst Availability"}, "BASIC_INFORMATION": {"apply_fail": "Failed to set device basic information", "apply_success": "Device basic information updated successfully", "contact": "Contact", "location": "Location", "model": "Model", "name": "Name", "title": "Basic Information"}, "BUTTON": {"add": "Add", "add_to_scheduler": "Add to Scheduler", "agree": "Agree", "apply": "Apply", "audit": "Audit", "back": "Back", "cancel": "Cancel", "change": "Change", "check": "Check", "checkNow": "Check Now", "clear": "Clear", "clear_fail_record": "Clear failure records", "close": "Close", "compare": "Compare", "confirm": "Confirm", "connected": "Connected", "continue": "Continue", "copy": "Copy", "create": "Create", "deactivate": "Deactivate", "decline": "Decline", "delete": "Delete", "disable_new_version_notifications": "Disable new version notifications", "disconnected": "Disconnected", "download": "Download", "download_all_logs": "Download All Logs", "download_filter_logs": "Download Filter Logs", "edit": "Edit", "enable_new_version_notifications": "Enable new version notifications", "execute": "Execute", "faqs": "FAQs", "finish": "Finish", "got_it": "Got It", "ignore": "Ignore", "leave": "Leave", "next": "Next", "ok": "OK", "query": "Query", "reboot": "Reboot", "redirect": "Redirect", "refresh": "Refresh", "regenerate": "Regenerate", "register": "Register", "resend": "Resend", "reset": "Reset", "retry_failed_devices": "Retry Failed Devices", "run": "Run", "save": "Save", "scan": "<PERSON><PERSON>", "search": "Search", "security_patch_available": "Security patch available", "select": "Select", "select_firmware_version": "Select firmware version", "send": "Send", "send_test_eamil": "Send Test Email", "set": "Set", "unregister": "DEREGISTER", "upgrade": "Upgrade", "upgrade_firmware": "Upgrade firmware", "upload": "Upload", "verify": "Verify", "verify_email": "<PERSON><PERSON><PERSON>"}, "cli_object_database": {"add_cli_fail": "Unable to create CLI script", "add_cli_object": "Add CLI Script", "add_cli_success": "New CLI script created successfully", "before_date": "Date", "before_time": "Time", "cli_objects": "CLI Scripts", "cli_script": "CLI Script", "delete_all_execution_results": "Delete All CLI Script Execution Results", "delete_all_execution_results_before_time": "Delete Script Execution Results Prior to", "delete_all_execution_results_before_time_desc": "Are you sure you want to delete all script execution results prior to {{param}}?", "delete_all_execution_results_desc": "Are you sure you want to delete all CLI script execution results?", "delete_cli_fail": "Unable to delete CLI script", "delete_cli_object": "Delete CLI Script", "delete_cli_object_desc": "Are you sure you want to delete this CLI script?", "delete_cli_object_disabled": "This script cannot be deleted because it is linked to a scheduled task or script automation.", "delete_cli_objects_desc": "Are you sure you want to delete these CLI scripts?", "delete_cli_success": "CLI script deleted successfully", "delete_execution_result_fail": "Unable to delete execution results", "delete_execution_result_success": "Script execution results deleted successfully", "delete_results_before_time": "Delete CLI Script Execution Results", "description": "Description", "download_all_execution_results": "Download All Execution Results", "download_all_execution_results_fail": "Unable to download execution results", "download_execution_results_failed_hint": "No execution results available for download.", "edit_cli_fail": "Unable to update CLI script", "edit_cli_object": "Edit CLI Script", "edit_cli_success": "CLI script updated successfully", "execution_results": "Execution Results", "get_cli_fail": "Unable to retrieve CLI scripts", "linked_scheduled_task": "Linked Scheduled Tasks", "linked_script_automation": "Linked Script Automations", "name": "Name", "non_ascii": "Only ASCII characters are accepted.", "scheduled_execution_cli_object_desc": "You can create scheduled tasks to run CLI scripts at a specified date and time from the Administration > Maintenance Scheduler page.", "scheduled_execution_cli_object_info": "Scheduled Script", "scheduled_task": "Scheduled Task", "title": "Saved C<PERSON><PERSON>"}, "COMBO_BOX": {"disabled": "Disabled", "enabled": "Enabled", "export_all_event_csv": "Export All Events to CSV", "export_all_syslog_csv": "Export All Syslog to CSV", "export_csv": "Export CSV", "export_pdf": "Export PDF", "sequential": "Strict Sequential", "smart_concurrent": "Smart Sequential"}, "COMMAND_BAR": {"hide_automation_button": "<PERSON><PERSON> Widget", "hide_button_panel": "<PERSON>de Button Panel", "hide_detail": "Hide Details", "hide_group": "Hide Groups", "list_view": "List View", "show_automation_button": "Show Button Widget", "show_button_panel": "Show Button Panel", "show_detail": "View Details", "show_group": "View Groups", "topology_view": "Topology View"}, "CONFIG_CENTER": {"alias_name": "<PERSON>as Name", "backup_config": "Back Up Configuration", "backup_message": "MXview One will archive these configuration files", "backup_tab": "Backup", "backup_tab_hint": "Please go to the Backup tab and export a device configuration first", "compare_config_basement": "Compare Basement: {{compareConfigFileName}}", "compare_config_dialog_title": "Compare Configurations", "compare_tab": "Records", "compare_target": "Compare Target", "configuration_file": "Configuration File", "configuration_name": "Configuration Name", "create_time": "Creation Time", "delete_config_dialog_title": "Delete Configuration", "delete_config_failed": "Failed to delete device configuration", "delete_config_success": "Device configuration deleted successfully", "delete_config_warning_message": "Are you sure you want to delete the selected configuration?", "device_list": "Device List", "export_failed": "Export failed", "export_success": "Export successful", "from_date": "Start Date", "group_name": "Group", "ip_address": "IP Address", "last_check_time": "Last Check Time", "local_file": "Local File", "restore_config": "Restore Configuration", "restore_device": "Restore Device - {{selectedDeviceIP}}", "restore_tab": "Rest<PERSON>", "site_name": "Site", "time": "Time", "title": "Device Configuration Center", "to_date": "End Date"}, "DASHBOARD": {"adpDestIp": "Top 5 ADP Policy Events by Destination IP", "adpSrcIp": "Top 5 ADP Policy Events by Source IP", "ap_devices": "AP Devices", "ap_traffic_load": "AP Traffic Load", "baseline": "Basic", "client_devices": "Client Devices", "critial_devices": "Critical Devices", "device_availability": "Device Availability", "device_availability_intro": " ", "device_summary": "<PERSON><PERSON>", "devices": "Devices", "disk_space_utilization": "Disk Space Utilization", "dpiDestIp": "Top 5 Protocol Filter Policy Events by Destination IP", "dpiSrcIp": "Top 5 Protocol Filter Policy Events by Source IP", "event_highlight": "Event Highlights", "healthy_devices": "Healthy Devices", "icmp_unreachable": "ICMP Unreachable", "iec_level_1": "Medium", "iec_level_2": "High", "ipsDestIp": "Top 5 IPS Policy Events by Destination IP", "ipsSrcIp": "Top 5 IPS Policy Events by Source IP", "l3DestIp": "Top 5 Layer 3-7 Policy Events by Destination IP", "l3SrcIp": "Top 5 Layer 3-7 Policy Events by Source IP", "last_1_day": "Last 24 hours", "last_1_hours": "Last 1 hour", "last_1_weeks": "Last week", "last_2_weeks": "Last 2 weeks", "last_24_hours": "Last 24 hours", "last_3_days": "Last 3 days", "last_3_hours": "Last 3 hours", "last_30_days": "Last 30 days", "last_30_minutes": "Last 30 minutes", "last_7_days": "Last 7 days", "last_update": "Last Update:", "link_down": "Link Down", "link_up": "Link Up", "linkButton": "Show Event Log", "not_pass": "Not Pass", "now": "From now", "open": "Open", "pass": "Pass", "reboot_times": "Cold/Warm Start Trap", "refresh_all": "Refresh All", "security_level": "Security Level", "security_summary": "Security Summary", "selecting_visible_item": "Selecting Visible Items", "set_default_tab": "Set as <PERSON><PERSON><PERSON>", "tabs": {"cybersecurity": "Cybersecurity", "general": "General", "wireless": "Wireless"}, "title": "Dashboard", "total_availability": "Device availability is below {{param}}%", "total_devices": "Total Devices", "unknown": "Unknown", "view_network_topology": "View Network Topology", "warning_devices": "Warning Devices", "wireless_device_summary": "Wireless Device Summary"}, "DATABASE_BACKUP": {"database_name": "Name", "fail": "Failed to back up database", "success": "Database backed up to {{param1}}", "title": "Database Backup"}, "DEVICE_LOCATOR": {"stop_trigger_locator": "Stop", "title": "<PERSON><PERSON>or", "trigger_locator": "Start", "trigger_locator_fail": "Failed to trigger device locator", "trigger_locator_off": "Device locator off", "trigger_locator_on": "Device locator on"}, "device_management": {"built_in_command_execution_process_is_running": "Unable to send command. Another command is running. Try again later.", "execute_fail": "Failed to execute", "limited_support": "Limited support, please check user manual.", "select_device": "Select Device", "select_operation": "Select Operation"}, "DEVICE_PANEL": {"panel_status": "Panel Status", "panel_zoom_size": "Device panel zoom size", "port": "Port"}, "DEVICE_POLLING_SETTING": {"consecutive_icmp_fail_trigger": "Consecutive failures before triggering an ICMP unreachable event", "consecutive_snmp_fail_trigger": "Consecutive failures before triggering an SNMP unreachable event", "icmp_polling_interval": "ICMP Polling Interval", "snmp_polling_interval": "SNMP Polling Interval", "title": "Polling Settings"}, "DEVICE_PROPERTIES": {"basic_property": {"alias": "<PERSON><PERSON>", "availability": "Availability", "bios_version": "BIOS/Bootloader Version", "cpu_loading": "CPU Load (%)", "cpu_loading_30_seconds": "Avg. CPU load - 30 seconds (%)", "cpu_loading_300_seconds": "Avg. CPU load - 300 seconds (%)", "cpu_loading_5_seconds": "Avg. CPU load - 5 seconds (%)", "cpu_utilization_300_seconds": "CPU Utilization in the Last 300 Seconds (%)", "cpu_utilization_60_seconds": "CPU Utilization in the Last 60 Seconds (%)", "cpu_utilization_900_seconds": "CPU Utilization in the Last 900 Seconds (%)", "disk_utilization_unit": "Disk Utilization (%)", "fw_system_version": "Firmware/System Image Version", "fw_version": "Firmware/System Image Version", "mac_address": "MAC Address", "memory_usage": "Memory Usage", "memory_usage_unit": "Memory Usage (%)", "model_name": "Model Name", "os_type": "Operating System", "power_comsumption": "Power Consumption (W)", "serial_number": "Serial Number", "system_contact": "System Contact", "system_description": "System Description", "system_location": "System Location", "system_name": "System Name", "system_object_id": "System Object ID", "system_up_time": "System Uptime", "title": "Basic Device Properties"}, "cellular": {"cellular_carrier": "Cellular Carrier", "cellular_ip_address": "Cellular IP Address", "cellular_mode": "Cellular Mode", "cellular_signal": "Cellular Signal", "imei": "IMEI", "imsi": "IMSI", "title": "Cellular Information"}, "goose_table": {"app_id": "APP ID", "gocb_name": "GoCB Name", "goose_address": "GOOSE Address", "ied_name": "IED Name", "port": "Ingress Port", "rx_counter": "RX Counter", "status": "Status", "tampered_port": "Tampered Port", "tampered_port_status": "Port {{ port }} has been tampered with", "title": "GOOSE Check", "type": "Type", "vid": "VID"}, "ipsec": {"l2tp_status": "L2TP Status", "local_gateway": "Local Gateway", "local_subnet": "Local Subnet", "name": "IPSec Name", "phase_1_status": "Phase 1 Status", "phase_2_status": "Phase 2 Status", "remote_gateway": "Remote Gateway", "remote_subnet": "Remote Subnet", "title": "IPsec Status"}, "link": {"from": "From", "port": "Port", "sfpTitle": "SFP Information", "speed": "Link Speed", "title": "Link Information", "to": "To"}, "management_interfaces": {"http_port": "HTTP Port", "https_port": "HTTPS Port", "profinet_enabled": "PROFINET Enabled", "ssh_port": "SSH Port", "telnet_port": "Telnet Port", "title": "Management Interfaces"}, "mms": {"title": "MMS Properties"}, "modbus_device_property": {"model": "Model", "revision": "Revision", "title": "Modbus Device Properties", "vendor": "<PERSON><PERSON><PERSON>"}, "not_selected": "Select a module to show device details", "other_device_properties": {"active_redundancy_protocol": "Active Redundancy Protocol", "auto_ip_config": "Auto IP Config", "default_gateway": "Default Gateway", "dns_1_ip_address": "DNS 1 IP address", "dns_2_ip_address": "DNS 2 IP address", "ip_ad_ent_addr": "ipAdEntAddr", "ip_address": "IP Address (mib)", "mac_address": "MAC Address (mib)", "model_name": "Model Name", "monitor_current_mode": "Monitor Current Mode", "monitor_down_stream_rate": "Monitor Down Stream Rate", "monitor_snr": "Monitor SNR", "monitor_up_stream_rate": "Monitor Up Stream Rate", "netmask": "Netmask", "title": "Other Device Properties"}, "port": {"if_number": "ifNumber", "interface": "interface", "number_of_ports": "Number of ports", "poe_port_class": "PoE Port class", "poe_power_legacy_pd_detect": "PoE Power Legacy PD Detect", "poe_power_output_mode": "PoE Power output mode", "title": "Port Information"}, "power": {"power_1_status": "Power 1 Status", "power_2_status": "Power 2 Status", "title": "Power Status"}, "redundancy": {"active_redundancy_protocol": "Active Redundancy Protocol", "dh": "Dual Homing", "iec_624393_redundancy_protocol": "IEC 62439-3 Redundancy Protocol", "rstp": "RSTP", "tc": "Turbo Chain", "title": "Redundancy", "trv2": "Turbo Ring V2"}, "selected_module": "Selected Module", "snmp": {"1st_trap_community": "1st Trap Community", "2nd_trap_server_community": "2nd Trap Community", "inform_enabled": "Inform Enabled", "inform_retries": "Inform Retries", "inform_timeout": "Inform Timeout", "read_community": "Read Community", "title": "SNMP Information", "trap_server_address_1": "Trap Server Address 1", "trap_server_address_2": "Trap Server Address 2"}, "title": "Device Properties", "vpn": {"from_ip": "VPN From IP", "local_connection_name": "Local VPN Connection Name", "remote_connection_name": "Remote VPN Connection Name", "to_ip": "VPN To IP"}, "wireless": {"channel_width": "Channel Width", "client_ip": "Client IP", "client_mac": "Client MAC", "client_RSSI": "Client Signal Strength (dBm)", "rf_type": "RF Type", "ssid_index": "SSID Index", "title": "Wireless Information", "vap_mgmt_encryption": "VAP Management Encryption", "vap_wpa_encrypt": "VAP WPA Encryption", "vapAuthType": "VAP Authentication Type"}}, "DEVICE_SETTING": {"advanced": "Advanced", "alias": "<PERSON><PERSON>", "alias_input_invalid": "Please enter a valid device alias", "apply_fail": "Failed to update device parameters", "apply_success": "Device parameters updated successfully", "availability_time_frame": "Timeframe for availability calculation", "get_parameter_fail": "Failed to get device parameters", "input_error_message": "Please enter a valid value", "modify_device_alias": "Modify <PERSON><PERSON>", "password": "Password", "password_input_invalid": "Please enter a valid password", "polling_interval": "Polling Interval", "polling_ip": "Polling IP", "snmp_configuration": "SNMP Configuration", "snmp_port_invalid": "Please enter a valid SNMP port", "title": "<PERSON><PERSON>s", "use_global": "Use Global Access Username and Password", "use_global_device_settings": "Use Device Setting Template", "username": "Username", "username_input_invalid": "Please enter a valid username"}, "DEVICE": {"device_properties": "Device Properties", "device_role": "<PERSON><PERSON> Role", "filter_device": "Type to filter devices", "filter_register_device": "Type to filter registered devices", "na": "N/A", "properties": {"availability": "Availability", "device_alias": "<PERSON><PERSON>", "device_ip": "Device IP", "firmware_version": "Firmware Version", "location": "Location", "mac_address": "MAC Address", "model_name": "Model Name", "mxsec_flag": "Security Add-On", "severity": "Severity"}, "registered_devices": "Registered", "site_name": "Site Name", "title": "Device List", "unregistered_devices": "Unregistered"}, "DeviceDashboard": {"avg_erase_count": "Avg. <PERSON><PERSON>", "change_disk_hint": "Please change your disk", "chartTitle": {"60s_cpu": "CPU Utilization (Last 60 Seconds)", "connection": "Connection Status", "cpu": "CPU Utilization", "disk": "Disk Utilization", "memory": "Memory Utilization", "noiseFloor": "Noise Floor", "raid_mode": "RAID Mode", "signalStrength": "Signal Strength", "smart": "S.M.A.R.T.​", "snr": "SNR", "traffic": "Traffic Load"}, "connected": "Connected", "current_status": "Current status:", "cycle_limitation": "Cycle Limitation", "icmp_not_support": "ICMP devices do not support this feature", "link_down_port": "Link Down Ports", "link_up_port": "Link Up Ports", "managed": "Managed", "migrating_data": "Migrating Data", "no_raid": "No RAID", "normal": "Normal", "raid": "RAID", "rebuild": "Rebuild", "smart_hint": "(Self-Monitoring Analysis and Reporting Technology) represents disk health status and lifespan information", "unreachable": "{{ warningWording }} unreachable. MXview One may not get complete data from the device."}, "DIALOG": {"add_wifi_ssid": {"aes": "AES", "clear_all_existing_ssid": "Clear All Existing SSIDs", "eapol_version": "EAPOL Version", "encryption": "Encryption", "open": "Open", "passphrase": "Passphrase", "personal": "Personal", "protected_management_frame": "Protected Management Frame", "rf_band": "RF Band", "security": "Security", "ssid": "SSID", "title": "Add Wi-Fi SSID", "tkip_aes_mixed": "TKIP / AES Mixed", "wpa_mode": "WPA Mode"}, "auto_layout": {"desc": "Are you sure you want to use Auto Layout? (current layout will be overridden)", "title": "Auto Layout"}, "auto_topology": {"advanced": "Advanced Topology Analysis", "advanced_desc": "*Additional time is required.", "advanced_hint": "Draw links between an ICMP device and a device that supports either LLDP or forwarding tables", "fail": "Auto Topology failed", "link_check": "Strict Link Verification Mode", "link_check_hint": "If enabled, links between devices will only be shown on the topology if the devices on both ends have the other device's information in their LLDP table.", "new_topology": "New Topology", "new_topology_desc": "Existing links are going to be deleted", "success": "Auto Topology successful", "title": "Auto Topology", "update_topology": "Update Topology", "update_topology_desc": "Existing links will be kept while new links are added"}, "background_dialog": {"content": "Please set a background image first.\n The background image can be a floor plan or other image that represents the coverage area.", "set_now": "Set Now", "title": "Set Background Image"}, "change_group": {"assign_to_group": "Assign to Group", "change_group_fail": "Failed to move devices to the new group", "change_group_success": "<PERSON><PERSON> moved to the new group successfully", "current_group": "Current Group", "ip": "IP Address", "title": "Change Group"}, "change_wifi_channel": {"channel": "Channel", "channel_width": "Channel Width", "execute_button": "Change", "title": "Change Wi-Fi Channel"}, "create_group": {"assign_group": "Assign to Group", "create_group_fail": "Failed to create group", "create_group_success": "Group created successfully", "current_group": "Current Group", "empty_group_name": "You must enter a name for the group", "group_desc": "Group Description", "group_name": "Group Name", "parent_group": "Parent Group", "title": "Create Group"}, "create_snapshot": {"execute_button": "Create", "title": "Create Snapshot"}, "data_not_ready": {"content": "The device data is not ready yet, please try again later.", "title": "Please Try Again Later"}, "delete_account": {"delete_confirm_message": "Are you sure you want to delete this account?", "title": "Delete Account"}, "delete_background": {"desc": "Are you sure you want to delete the background image?", "failed": "Failed to delete background image", "success": "Background image deleted successfully", "title": "Delete Background"}, "delete_custom_opc": {"delete_confirm_message": "Are you sure you want to delete this custom OPC Tag?", "title": "Delete Custom OPC Tag"}, "delete_device": {"delete_wireless_client_alert": "The historical data of deleted wireless client device(s) will also be deleted. This will affect the traceability of listed features. Do you want to proceed?", "delete_wireless_client_alert_title": "Confirmation of <PERSON><PERSON>(s) Deletion", "desc": "Are you sure you want to delete this device?", "desc_multi": "Are you sure you want to delete these devices?", "failed": "Failed to delete device(s)", "success": "Device(s) deleted successfully.", "title": "Delete Device"}, "delete_group": {"desc": "Are you sure you want to delete this group?", "desc_multi": "Are you sure you want to delete these groups?", "failed": "Failed to delete group(s)", "success": "Group(s) deleted successfully", "title": "Delete Group"}, "delete_link": {"desc": "Are you sure you want to delete this link?", "desc_multi": "Are you sure you want to delete these links?", "failed": "Failed to delete link(s)", "success": "Link(s) deleted successfully", "title": "Delete Link"}, "delete_objects": {"desc": "Are you sure you want to delete all the selected objects?", "failed": "Failed to delete selected objects", "success": "Selected objects deleted successfully", "title": "Delete Objects"}, "delete_site": {"desc": "Are you sure you want to delete this site?", "failed": "Failed to delete site", "success": "Site deleted successfully", "title": "Delete Site"}, "device_settings": {"fail": "Failed to update Link Budget Parameters (Per-device)", "rx_antenna_gain": "RX Antenna Gain", "rx_cable_loss": "RX Cable Loss", "success": "Link Budget Parameters (Per-device) updated successfully", "title": "Link Budget Parameters (Per-device)", "tx_antenna_gain": "TX Antenna Gain", "tx_cable_loss": "TX Cable Loss"}, "disable_unsecured": {"execute_button": "Disable HTTP and Telnet", "title": "Disable Insecure HTTP and Telnet Console"}, "disable_unused": {"execute_button": "Disable Unused Ports", "keep_port_available": "Keep in-use ports with temporary outage active", "title": "Disable Unused Ethernet and Fiber Ports"}, "discovery_device": {"another_discovery_error": "Another device discovery is in progress", "discovering": "Discovering devices", "discovery_finish": "Device discovery successful", "error": "Device discovery failed", "title": "<PERSON>ce Discovery"}, "dynamic_mac_sticky": {"address_limit": "Address Limit", "alias": "<PERSON><PERSON>", "mac_sticky": "Sticky MAC", "mac_sticky_settings": "Sticky MAC Settings", "packet_drop": "Drop Packet", "port": "Port", "port_duplicated": "This port has already been configured.", "port_format_not_equal": "Models must have the same port format and number of ports.", "port_selection_guide": "Port Name Key", "port_shutdown": "Shutdown Port", "security_action": "Security Action", "title": "Dynamic Sticky MAC"}, "export_config": {"config_center": "Configuration Center", "config_file": "Configuration File", "export": "Export", "fail": "Failed to export device configuration", "hint": "*Please make sure the username and password for this device are correctly set in the Device Accounts section.", "success": "Device configuration exported successfully", "title": "Export Config"}, "goose": {"how_to_resolve": "How to resolve?", "import_scd_tooltip": "Please import an SCD file to see GOOSE messages. Click \"Power\" > \"Import SCD\".", "ip_port": "Port {{ port }} of {{ ip }}", "open_web_console": "Open Web Console", "port_tampered_msg": "GOOSE Port Tampered was caused by:", "port_tampered_title": "Resolve GOOSE Port Tampered Issue", "reset_goose": "Reset Tampered GOOSE Message", "reset_goose_desc": "GOOSE message: {{ cbName }}/{{ appId }}/{{ mac }}", "reset_goose_title": "Are you sure you want to reset all instances of this tampered GOOSE message?", "resolve_goose_tampered_desc_1": "Try these steps to resolve the GOOSE Port Tampered issue", "resolve_goose_tampered_desc_2": "1. Check the IED(s) settings", "resolve_goose_tampered_desc_3": "Make sure the GOOSE publish/subscribe messages of the IED are set correctly.", "resolve_goose_tampered_desc_4": "2. Check the port status", "resolve_goose_tampered_desc_5": "Please check port {{ port }} status of {{ ip }}.", "resolve_goose_tampered_desc_6": "2. Check to make sure all devices are authorized", "resolve_goose_tampered_desc_7": "Please check to see if there are any unauthorized devices on the network.", "resolve_goose_tampered_desc_8": "Try these steps to resolve the GOOSE SA Tampered issue", "resolve_goose_timeout_desc_1": "Try these steps to resolve GOOSE Timeout issues.", "resolve_goose_timeout_desc_10": "Still not working?", "resolve_goose_timeout_desc_11": "Remove the SFP module and install it again.", "resolve_goose_timeout_desc_12_1": "If you have further questions, contact your", "resolve_goose_timeout_desc_12_2": "channel partner", "resolve_goose_timeout_desc_12_3": "first.", "resolve_goose_timeout_desc_13_1": "Contact", "resolve_goose_timeout_desc_13_2": "Moxa Technical Support", "resolve_goose_timeout_desc_13_3": "if you still need additional support.", "resolve_goose_timeout_desc_2": "1. Check the IED(s) settings", "resolve_goose_timeout_desc_3": "Make sure the GOOSE publish/subscribe messages of the IED are set correctly.", "resolve_goose_timeout_desc_4": "2. Make sure the port is not in link down status", "resolve_goose_timeout_desc_5": "Check to make sure the port of each device in the GOOSE flow ({{ cbName }}/{{ appId }}/{{ mac }}) is not in link down status.", "resolve_goose_timeout_desc_6": "3. Make sure the port does not have any TX/RX errors", "resolve_goose_timeout_desc_7": "Click on a link, choose \"Link Traffic\" to see the \"Packet Error Rate\" section. Make sure the port does not have any errors.", "resolve_goose_timeout_desc_8": "4. Check if the fiber ports exceed certain thresholds", "resolve_goose_timeout_desc_9": "Click \"SFP\" ➔ \"SFP List\". Make sure the ports do not exceed certain thresholds.", "sa_tampered_msg": "GOOSE SA Tampered", "sa_tampered_name_msg": "GOOSE message ({{ cbName }}/{{ appId }}/{{ mac }}) conflicts with another GOOSE source address.", "sa_tampered_title": "Resolve GOOSE SA Tampered Issue", "tampered": "Tampered", "timeout": "Timeout", "timeout_msg": "GOOSE Timeout was caused by:", "timeout_title": "Resolve GOOSE Timeout Issue"}, "import_config": {"config_center": "Configuration Center", "config_file": "Configuration File", "config_file_error": "MXview One only supports .ini config files", "config_file_size_error": "The maximum file size is 3 MB.", "fail": "Failed to import device configuration", "hint": "* Please make sure the username and password for this device are correctly set in \"Device Accounts\"", "import": "Import", "success": "Device configuration imported successfully", "title": "Import Config"}, "link_traffic": {"date": "Date", "from": "From", "packet_error_rate_title": "Packet Error Rate", "port_traffic_title": "Port Traffic", "time": "Time", "to": "To", "utilization": "Utilization", "value": "Value"}, "mac_sticky_switch": {"mac_sticky": "Sticky MAC", "title": "Sticky MAC On / Off"}, "maintain_group": {"change_icon": "Change group icon", "create": "Create", "create_group_fail": "Failed to create group", "create_group_success": "Group created successfully", "delete": "Delete", "delete_group_fail": "Failed to delete group", "delete_group_success": "Group deleted successfully", "empty_group_name": "You must enter a name for the group", "group_desc": "Group Description", "group_name": "Group Name", "modify_group_fail": "Failed to modify group", "modify_group_success": "Group modified successfully", "reset_icon": "Reset to default image", "title": "Group Maintenance"}, "ping": {"failed": "<PERSON> failed", "title": "<PERSON>"}, "policy_profile": {"delete_msg": "Are you sure you want to delete the selected profile(s)?", "delete_title": "Delete Profile(s)"}, "reboot": {"execute_button": "Reboot", "reboot_sequence": "Reboot Sequence", "title": "Reboot", "unable_determine_reboot_sequence_hint": "Unable to determine reboot sequence. Make sure the topology includes a computer with MXView One, and then try again."}, "relearn_dynamic_mac_sticky": {"execute_button": "Relearn", "title": "Relearn Dynamic Sticky MAC"}, "restore_to_create_snapshot": {"execute_button": "Rest<PERSON>", "title": "<PERSON>ore from Snapshot"}, "scd": {"import_failed_desc": "Take a look at the list of issues below, correct the SCD file, and try importing again.", "import_failed_title": "Failed to Import SCD File", "import_succeed_desc": "GOOSE messages and flow design successfully built into the network topology.", "import_succeed_title": "SCD File Import Successful", "missing": "Missing", "missing_device": "Can't find the following device(s):", "missing_device_desc": "Take a look at the list of issues below.", "scd_file_error": "MXview One only supports .scd files", "scd_file_size_error": "Maximum file size is 100 MB", "select_file": "Select File", "tag": "tag", "topology_change_desc_1": "Try these steps to resolve issues.", "topology_change_desc_2": "1. Add the missing device(s)", "topology_change_desc_3": "Click \"Edit\" ➔ \"Add Device\".", "topology_change_desc_4": "2. Import the SCD file again", "topology_change_desc_5": "Click \"Power\" ➔ \"Import SCD\".", "topology_change_title": "Problems Changing Topology", "visualize_goose_messages": "To visualize GOOSE messages between IEDs, you must import an SCD file first.", "visualize_goose_messages_title": "Visualize GOOSE Messages"}, "set_background": {"browse": "browse", "desc": "Drag a image here or ", "desc1": " to set the background", "failed": "Failed to set background image", "image": "Image", "image_alpha": "Alpha", "image_error": "The selected file is not an image", "image_position": "Position", "image_saturation": "Saturation", "image_x": "X", "image_y": "Y", "preview": "Preview", "size_error": "The image size must be between 1 KB and 20 MB", "success": "Background image set successfully", "title": "Set Background", "topology_size": "Topology Size"}, "set_document": {"current_filename": "Current Filename", "delete": "Delete Document", "error": "MXview One only supports PDF documents", "failed": "Failed to set document", "file_size_error": "The document must be a PDF and the maximum size is 20 MB.", "open": "Open Document", "set": "Set Document", "success": "Document set successfully", "title": "Set Document", "upload": "Select a file to upload"}, "set_port_label": {"error": "Error", "failed": "Failed to set port label", "from": "From:", "success": "Port label set successfully", "title": "Set Port Label", "to": "To:", "use_custom_label": "Use Custom Label", "vpn_link_desc": "Labels for VPN links are not configurable."}, "set_scale": {"error": "Parameter error", "fail": "Failed to set scale", "success": "Scale set successfully", "title": "Set Scale"}, "severity_threshold": {"bandwidth_input_invalid": "Please enter an integer from 0-100", "bandwidth_utilization": "Bandwidth Utilization", "critical": "Critical", "failed": "Failed to update severity threshold", "information": "Information", "over": "Over", "packet_error_rate": "Packet Error Rate", "sfp_rx_over": "SFP RX Over", "sfp_rx_under": "SFP RX Under", "sfp_temp_over": "SFP Temperature Over", "sfp_threshold": "SFP Threshold", "sfp_tx_over": "SFP TX Over", "sfp_tx_under": "SFP TX Under", "sfp_vol_over": "SFP Voltage Over", "sfp_vol_under": "SFP Voltage Under", "success": "Severity threshold updated successfully", "title": "Severity Threshold", "under": "Under", "warning": "Warning"}, "sfp_info": {"date": "Date", "dateError": "Invalid date range", "from": "From", "port": "Port", "sfpRxPower": "SFP RX", "sfpRxPower_label": "RX", "sfpRxPower_scale": " dBm", "sfpTemperature": "SFP Temperature", "sfpTemperature_label": "Temp.", "sfpTemperature_scale": " °C", "sfpTxPower": "SFP TX", "sfpTxPower_label": "TX", "sfpTxPower_scale": " dBm", "sfpVoltage": "SFP Voltage", "sfpVoltage_label": "Voltage", "sfpVoltage_scale": " V", "time": "Time", "title": "SFP Info", "to": "To", "value": "Value"}, "sfp_sync": {"confirm_desc": "Are you sure you want to sync the SFP Threshold from the device?", "content": "You can sync the SFP Threshold from a Moxa switch. After syncing, the Temperature, TX Power, and RX Power for Fiber Check will be synced to the SFP Threshold of each link.", "failed": "Failed to sync SFP Threshold", "hint": "*To check the SFP Threshold, you can click on a link, then choose Severity Threshold ➔ SFP Threshold", "success": "SF<PERSON> successfully synced", "title": "Sync the SFP Threshold From Devices"}, "wireless_settings": {"fail": "Failed to update Link Budget Parameters (General)", "rxSensitivityHigh": "RX Sensitivity High", "rxSensitivityLow": "RX Sensitivity Low", "rxSensitivityMedium": "RX Sensitivity Medium", "sr": "Reserved Safety Factor", "success": "Link Budget Parameters (General) updated successfully", "title": "Link Budget Parameters (General)"}}, "EMBED_WIDGET": {"click_preview": "Click to preview", "copied_to_clipboard": "Link copied to clipboard", "copy_link": "Copy link", "custom": "Customization", "desc": "Paste this into any HTML page", "embed": "Embed", "height": "Height", "layout_1": "Layout 1", "layout_2": "Layout 2", "layout_3": "Layout 3", "layout_4": "Layout 4", "link": "Link", "no_api_key": "You must create an API key first", "preview": "Preview", "recent_event": "Recent Events", "select_api_key": "Select API key", "select_layout": "Select Layout", "title": "Embedded Web Widget", "topology": "Topology", "topology_recent_event": "Topology and Recent Events", "width": "<PERSON><PERSON><PERSON>"}, "error_handler": {"error_session_expired_dialog": "Session expired. The system will redirect you to the login page."}, "ERROR_MESSAGE": {"get_data_fail": "Failed to retrieve data", "input_invalid_char": "Please enter a valid name", "input_invalid_characters": "This field may not contain any of the following characters: #%&*:<>?|{}\\\"/", "input_invalid_contact": "Please enter a valid contact", "input_invalid_email": "Please enter a valid email address", "input_invalid_location": "Please enter a valid location", "input_invalid_mac": "Invalid MAC address", "input_invalid_password_characters": "This field may not contain any of the following characters: '\\\"/`", "input_invalid_script": "This field may not contain any of the following characters: #%&*{}|:\"<>?/\\", "input_ip_invalid": "Please enter a valid IP address", "input_required": "This value is required", "non_restricted_ascii": "ASCII characters, except for ' \\\" ` \\\\"}, "errors": {"A001": "Missing required field", "A002": "Missing query string", "A003": "Incorrect format", "D001": "Maximum number of licenses reached", "D002": "The device cannot be found", "D003": "The device must be online", "D004": "The device was deleted", "F001": "The firmware cannot be found", "F002": "This firmware already exists", "F003": "Maximum number of firmware files reached", "G001": "This group already exists", "G002": "The group cannot be found", "G003": "The default group cannot be modified", "G004": "Admin users cannot be assigned to groups", "I001": "This interface already exists", "I002": "The interface cannot be found", "I003": "The default group cannot be modified", "I004": "This interface is referenced by a security profile.", "L001": "Invalid activation code", "L002": "The license has expired", "L003": "Duplicate activation code", "L004": "Maximum number of nodes reached", "L005": "The device cannot be activated or deactivated", "L006": "Invalid start time", "L007": "Invalid start time for the New type license", "O001": "This object already exists", "O002": "The object cannot be found", "O003": "This object is referenced by a security profile.", "P001": "The package cannot be found", "P002": "This package already exists", "P003": "Maximum number of packages reached", "P004": "Unsupported version", "S001": "Update db error", "SP001": "This profile already exists", "SP002": "The profile cannot be found", "T001": "Unauthorized token", "T002": "Token expired", "T003": "Invalid token", "U001": "Permission denied", "U002": "This username already exists", "U003": "The user cannot be found", "U004": "The role cannot be found", "U005": "Invalid username or password", "U006": "The password does not meet the minimum length", "U007": "The password exceeds the maximum length", "U008": "The password cannot be the same as the username", "U009": "Must include at least one uppercase character", "U010": "Must include at least one lowercase character", "U011": "Must include at least one digit", "U012": "Must include at least one non-alphanumeric character", "U013": "The password cannot be the same as the previous password", "U014": "invalid username", "Unknown": "Unknown error"}, "EULA": {"agree_hint": "Please agree with the agreement to use MXview One.", "eula_title": "EULA"}, "EVENT_MENU": {"ack": "Ack", "ack_all": "Ack All", "filter_event": "Filter Conditions"}, "EVENT": {"ack_all_events": {"ack_all_event_success": "All events acked successfully", "button_ack_hint": "Ack Selected Event(s)", "button_hint": "Ack All Events", "confirm_message": "All events will be acked. Are you sure you want to continue this process?", "unable_ack_all_event": "Failed to ack all events"}, "ack": {"ack_fail": "Failed to ack event", "acked": "Acked", "any": "Any", "unacked": "Unacked"}, "all_event": "Event History", "all_group": "All Groups", "all_site": "All sites", "clear_all_events": {"button_hint": "Clear All Events", "clear_all_event_success": "All events cleared successfully", "confirm_message": "All events will be cleared. Are you sure you want to continue this process?", "confirm_message_network": "All network and device events will be cleared. Are you sure you want to continue this process?", "confirm_message_system": "All system events will be cleared. Are you sure you want to continue this process?", "unable_clear_all_event": "Failed to clear all events"}, "custom_events": {"activate": "Enable Custom Event", "add_custom_event": "Add Custom Event", "all": "All", "all_devices": "All devices", "apply_fail": "Failed to add custom event", "apply_success": "Custom event added successfully", "below": "Below", "condition": "Condition", "condition_operator": "Condition Operator", "condition_value": "Condition Value", "consecutive_updates": "Consecutive Pollings", "delete_fail": "Failed to delete custom event(s)", "delete_success": "Custom event(s) deleted successfully", "description": "Description", "device_properties": "Device Properties", "devices": "Devices", "duration": "Duration", "equal": "Equal", "event_name": "Event Name", "filter_custom_event": "Type to filter custom events", "get_fail": "Failed to get custom events", "helper": "Add custom events and assign devices to them", "not_equal": "Not Equal", "over": "Over", "recovery_description": "Recovery Description", "register": "Register", "register_devices": "Registered Devices", "search": "Search", "severity": "Severity", "title": "Custom Event", "update_custom_event": "Update Custom Event", "update_fail": "Failed to update custom event", "update_success": "Custom event updated successfully"}, "event_description": {"abc_attache_warning": "ABC device attached", "abc_auto_import_warning": "Failed to auto import config", "abc_config_warning": "Failed to export config file", "abc_detache_warning": "ABC device detached", "abc_log_warning": "Failed to export log file", "abc_space_warning": "Not enough space on the ABC device", "abc_unauthorized_warning": "Unauthorized media detected", "abc_unknow_warning": "Unknown error", "abc02_warning": "USB event: {{param}}", "account_audit_baseline_failed": "Failed to complete account audit. Unable to retrieve data from all devices.", "account_audit_baseline_match": "Account audit completed successfully. The result matches the baseline.", "account_audit_failed": "Failed to complete account audit. Unable to retrieve data from devices at {{ip}}.", "account_audit_match": "Account audit completed successfully. The result matches the baseline.", "account_audit_mismatch": "Account audit completed successfully. The result does not match the baseline.", "account_audit_unable_retrieve_device": "Failed to complete account audit. Unable to retrieve data from all devices.", "accountAudit1": "Account audit does not match the baseline", "accountAudit2": "Failed to complete account audit", "all_event_clear": "All MXview One system events are cleared", "auth_fail": "Failed to authenticate device login.", "availability_down": "Device availability under threshold", "availability_down_recovery": "Recovered from device availability under threshold", "background_scan_found": "New device found (IP: {{ip}}).", "cli_button_event_all_failed": "{{user}} from IP: {{sourceIP}} executed the button: {{cliName}}. The execution result is All Failed.", "cli_button_event_all_finished": "{{user}} from IP: {{sourceIP}} executed the button: {{cliName}}. The execution result is All Finished.", "cli_button_event_all_partially_finished": "{{user}} from IP: {{sourceIP}} executed the button: {{cliName}}. The execution result is Partially Finished.", "cli_button_event_start": "{{user}} from IP: {{sourceIP}} executed the button: {{cliName}}.", "cli_saved_script_event": "{{user}} from IP: {{sourceIP}} executed the CLI command: {{cliName}}", "cli_script_event": "{{user}} from IP: {{sourceIP}} starts to execute the CLI.", "cold_start": "Cold start", "custom_event_detail": "{{param1}}. Threshold={{param2}}, value={{param3}}. {{param4}}", "custom_event_recovery": "Custom event recovered", "custom_event_recovery_detail": "{{param1}} recovered. Threshold={{param2}}, value={{param3}}. {{param4}}", "custom_event_trigger": "Custom event triggered", "cybersecurity_event_trigger": "Cybersecurity Event", "ddos_under_attack": "DoS attack discovered", "ddos_under_attack_recovery": "Recovered from DoS attack", "device_configuration_change": "The device configuration has changed.", "device_firmware_upgrade": "Device firmware updated", "device_infom_receive": "Device Inform Received", "device_lockdown_violation": "Device lock down violation", "device_power_down": "Power {{param}} off", "device_power_down_off_to_on": "PWR {{param}} Off > On.", "device_power_on": "Power {{param}} on", "device_power_on_to_off": "PWR {{param}} On > Off.", "device_snmp_reachable": "Device SNMP reachable", "device_snmp_unreachable": "Device SNMP unreachable", "di_off": "DI {{param}}} off", "di_on": "DI {{param}}} on", "disk_space_not_enough": "Available disk space is under threshold", "event_config_import": "New configuration file imported", "event_ddos_attack": "DoS attack detected", "event_ddos_attack_recovery": "Recovered from DoS attack", "event_dying_gasp": "System power down, device powered by capacitor", "event_eps_is_off": "PoE external power source off", "event_eps_is_on": "PoE external power source on", "event_firewall_attack": "Firewall policy violation", "event_firewall_attack_recovery": "Recovered from firewall policy violation", "event_ieee_lldp_table_change": "{{trapDetail}}", "event_ieee_rstp_root_change": "RSTP new root selected in topology", "event_ieee_rstp_topology_change": "Topology changed by RSTP", "event_linux_account_setting_change": "Account settings for {{username}} changed", "event_linux_config_change": "The {{modules}} configuration was modified by {{username}}.", "event_linux_config_import": "Configuration import {{successOrFail}} by {{username}}", "event_linux_config_import_failed": "failed", "event_linux_config_import_succeed": "successful", "event_linux_coupling_change": "Turbo Ring v2 coupling path status changed", "event_linux_di_off": "Digital input {{index}} turned off", "event_linux_di_on": "Digital input {{index}} turned on", "event_linux_dotlx_auth_fail": "802.1X authentication failed on port {{portIndex}} with {{reason}}", "event_linux_dual_homing_change": "Dual Homing path switched", "event_linux_log_capacity_threshold": "Number of event log entries {{value}} has reached the threshold", "event_linux_low_input_voltage": "Input voltage of the power supply is below threshold", "event_linux_master_change": "Ring {{index}} master changed", "event_linux_master_mismatch": "Ring {{index}} master settings do not match", "event_linux_over_power_budget_limit": "Consumed power {{value}} of all PDs exceeded the maximum input power {{threshold}}", "event_linux_password_change": "Password for {{username}} changed", "event_linux_pd_no_response": "PoE port {{portIndex}} device not responding to PD failure check", "event_linux_pd_over_current": "Current of PoE port {{portIndex}} exceeded the safety limit", "event_linux_pd_power_off": "PoE port {{portIndex}} PD power off", "event_linux_pd_power_on": "PoE port {{portIndex}} PD power on", "event_linux_port_recovery_by_ratelimit": "Port {{portIndex}} recovered from shutdown due to traffic exceeding the rate limit", "event_linux_port_shutdown_by_ratelimit": "Port {{portIndex}} is blocked due to traffic exceeding the rate limit.", "event_linux_port_shutdown_by_security": "Port {{portIndex}} shut down by port security", "event_linux_power_detection_fail": "PoE port {{portIndex}} device is {{devicetype}}, please {{suggestion}}", "event_linux_power_detection_fail_devietype_na": "N/A", "event_linux_power_detection_fail_devietype_noPresent": "not present", "event_linux_power_detection_fail_devietype_unknown": "unknown", "event_linux_power_detection_fail_suggestion_disable_POE": "disable PoE", "event_linux_power_detection_fail_suggestion_enable_legacy": "enable legacy", "event_linux_power_detection_fail_suggestion_enable_POE": "enable PoE", "event_linux_power_detection_fail_suggestion_no": "check your connection", "event_linux_power_detection_fail_suggestion_rais_EPS_voltage": "raise EPS voltage", "event_linux_power_detection_fail_suggestion_select_auto": "select auto", "event_linux_power_detection_fail_suggestion_select_force": "select force", "event_linux_power_detection_fail_suggestion_select_high_power": "select high power", "event_linux_power_off": "Power {{index}} turned off", "event_linux_power_on": "Power {{index}} turned on", "event_linux_redundant_port_health_check": "Redundant port {{portIndex}} health check failed", "event_linux_RMON_trap_is_falling": "RMON Trap is falling", "event_linux_RMON_trap_is_raising": "RMON Trap is rising", "event_linux_rstp_invalid_bpdu": "RSTP port {{portIndex}} received an invalid BPDU (type: {{type}}, value:{{value}})", "event_linux_rstp_migration": "Port {{portIndex}} changed from {{originTopology}} to {{changeTopology}}", "event_linux_rstp_new_port_role": "RSTP port {{portIndex}} role changed from {{originalRole}} to {{newRole}}", "event_linux_ssl_cer_change": "SSL certificate changed", "event_linux_topology_change": "The topology has changed.", "event_linux_topology_change_by_type": "Topology changed by {{topologyType}}", "event_linux_user_login_lockout": "{{username}} locked due to {{param}} failed login attempts", "event_linux_user_login_success": "{{username}} successfully logged in via {{interface}}", "event_log_cleared_trap_event_info": "The event logs were cleared (User: {{ user }}, IP: {{ ip }}, Interface: {{ interface }}).", "event_message_serial_device_port_any_recovery": "Serial Port {{portnum}} has resumed normal operation.", "event_message_serial_device_port_break": "Serial Port {{portnum}} received an error: Break Error Count.", "event_message_serial_device_port_frame": "Serial Port {{portnum}} received an error: Frame Error Count.", "event_message_serial_device_port_overrun": "Serial Port {{portnum}} received an error: Overrun Error Count.", "event_message_serial_device_port_parity": "Serial Port {{portnum}} received an error: Parity Error Count.", "event_message_serial_device_port_rx": "The RX of serial port {{portnum}} did not receive any data in last {{min}} minute(s).", "event_message_serial_device_port_rx_recovery": "The RX of serial port {{portnum}} has resumed receiving data.", "event_message_serial_device_port_rxtx": "The RX & TX of serial port {{portnum}} did not receive any data in last {{min}} minute(s).", "event_message_serial_device_port_rxtx_recovery": "The RX & TX of serial port {{portnum}} has resumed receiving data.", "event_message_serial_device_port_tx": "The TX of serial port {{portnum}} did not receive any data in last {{min}} minute(s).", "event_message_serial_device_port_tx_recovery": "The TX of serial port {{portnum}} has resumed receiving data.", "event_not_found_type": "Unknown event type: {{ eventType }}", "event_pd_check_fail": "PD failure check (no response)", "event_pd_over_current": "PoE port {{portIndex}} over-current/short-circuit", "event_pd_power_off": "PoE port {{portIndex}} power off", "event_pd_power_on": "PoE port {{portIndex}} power on", "event_prp_function_fail": "PHR function failed", "event_serial_device_port_break": "Serial Port received an error: Break Error Count", "event_serial_device_port_frame": "Serial Port received an error: <PERSON><PERSON>", "event_serial_device_port_overrun": "Serial Port received an error: <PERSON><PERSON>", "event_serial_device_port_parity": "Serial Port received an error: <PERSON><PERSON> Error Count", "event_serial_device_port_rx": "The RX of serial port did not receive any data", "event_serial_device_port_rxtx": "The RX & TX of serial port did not receive any data", "event_serial_device_port_tx": "The TX of serial port did not receive any data", "event_sfp_rx_below": "The RX power ({{currentdB}} dBm) of SFP port {{portIndex}} is below the threshold ({{thresholddB}} dBm).", "event_sfp_rx_below_recovery": "SFP port {{portIndex}} RX {{recoverydB}}dBm recovered", "event_sfp_temp_over": "The module temperature ({{currentTemp}}ºc) of SFP port {{portIndex}} has exceeded the threshold ({{currentTemp}}ºc).", "event_sfp_temp_over_recovery": "SFP port {{portIndex}} temperature {{recoveryTemp}}ºc recovered", "event_sfp_tx_below": "The TX power ({{currentdB}} dBm) of SFP port {{portIndex}} is below the threshold ({{thresholddB}} dBm).", "event_sfp_tx_below_recovery": "SFP port {{portIndex}} TX {{recoverydB}}dBm recovered", "event_sfp_voltage_below": "The module voltage ({{currentVoltage}} V) of SFP port {{portIndex}} is below the threshold ({{thresholdVoltage}} V).", "event_sfp_voltage_below_recovery": "SFP port {{portIndex}} voltage {{recoveryVoltage}}V recovered", "event_sfp_voltage_over": "The module voltage ({{currentVoltage}} V) of SFP port {{portIndex}} has exceeded the threshold ({{thresholdVoltage}} V).", "event_sfp_voltage_over_recovery": "SFP port {{portIndex}} voltage {{recoveryVoltage}}V recovered", "event_too_many_login_failure": "Access to the web interface temporary blocked due to too many login failures.", "event_too_many_login_failure_recovery": "Recovered from too many login failures, web access resumed", "event_tracking_port_enabled_status": "A port enabled-related tracking entry changed", "event_tracking_static_route_status_changed": "A static route-related tracking entry changed", "event_tracking_status_changed": "A tracking entry status changed", "event_tracking_vrrp_status_changed": "A VRRP-related tracking entry changed", "event_trusted_access_attack": "Device trust access policy violation", "event_trusted_access_attack_recovery": "Recovered from device trust access policy violation", "event_user_info_change": "Account information changed: {{trapoid}}", "event_v3_trap_parse_error": "V3 Trap parse error", "event_v3_trap_parse_error_recovery": "V3 Trap parse error event cleared", "exceed_poe_threshold": "Exceeded PoE system threshold", "fan_module_malfunction": "Fan module malfunction.", "fiber_warning": "{{portIndex}} Fiber warning( {{warningType}} )", "firewall_policy_violation": "Firewall policy and DoS rule: {{trapoid}}", "firewall_under_attack": "Firewall policy violation", "firewall_under_attack_recovery": "Recovered from firewall policy violation", "firmware_upgraded": "Firmware upgraded", "firmware_version_release": "A firmware update is available. Check the Firmware Management page for details.", "goose_healthy": "GOOSE status: Healthy \nGOOSE message {{ display }}", "goose_healthy_with_value": "GOOSE status: Healthy \nGOOSE message {{ display }}", "goose_tampered": "GOOSE status: Tam<PERSON>", "goose_tampered_with_value": "GOOSE status: Tampered \nGOOSE message {{ display }}", "goose_timeout": "GOOSE status: Timeout", "goose_timeout_with_value": "GOOSE status: Timeout \nGOOSE message {{ display }}", "high_cpu_loading": "CPU loading has exceeded 85% for 10 minutes consecutively.", "icmp_packet_loss_over_critical_threhold": "Device ICMP packet loss rate up to {{param1}} (Over critical threshold {{param2}})", "icmp_packet_loss_over_critical_threhold_recovery": "Device ICMP packet loss is under {{param1}} (Over critical threshold {{param2}})", "icmp_packet_loss_over_threhold": "Device ICMP packet loss rate up to {{param1}} (Over threshold {{param2}})", "icmp_packet_loss_over_threhold_recovery": "Device ICMP packet loss rate is under {{param1}} (Over threshold {{param2}})", "icmp_reachable": "Device ICMP reachable", "icmp_unreachable": "Device ICMP unreachable", "iei_fiber_warning": "Fiber warning triggered", "input_bandwidth_over_threshold": "The input bandwidth utilization has exceeded the threshold.", "input_bandwidth_over_threshold_disabled": "No threshold setting for input bandwidth utilization for port {{portIndex}}.", "input_bandwidth_over_threshold_recovery": "Port {{portIndex}} input bandwidth utilization recovered, value is {{currentValue}}.", "input_bandwidth_over_threshold_with_port": "The input bandwidth utilization ({{currentValue}}) of port {{portIndex}} has exceeded the threshold ({{threshold}}).", "input_bandwidth_under_threshold": "The input bandwidth utilization is below the threshold.", "input_bandwidth_under_threshold_disabled": "No threshold setting for input bandwidth utilization for port {{portIndex}}.", "input_bandwidth_under_threshold_recovery": "Port {{portIndex}} input bandwidth utilization recovered, value is {{currentValue}}.", "input_bandwidth_under_threshold_with_port": "The input bandwidth utilization ({{currentValue}}) of port {{portIndex}} is below the threshold ({{threshold}}).", "input_packet_error_over_threshold": "The input packet error rate has exceeded the threshold.", "input_packet_error_over_threshold_disabled": "No threshold setting for input packet error rate for port {{portIndex}}.", "input_packet_error_over_threshold_recovery": "Port {{portIndex}} input packet error rate recovered, value is {{currentValue}}.", "input_packet_error_over_threshold_with_port": "The input packet error rate ({{currentValue}}) of port {{portIndex}} has exceeded the threshold ({{threshold}}).", "insufficient_disk_space": "Less than 5 GB of disk space available.", "interface_set_as_ospf_designated_router": "Interface set as OSPF designated router.", "ip_conflict_detected": "IP conflict detected for {{ip}}, conflicting MACs:", "ip_conflict_detected_failed": "IP conflict detection cannot run because Npcap/WinPcap/Libpcap is not found", "ip_conflict_recovery": "IP conflict resolved.", "iw_client_joined": "Client joined: {{param}}", "iw_client_left": "Client left: {{param}}", "l3_firewall_policy_violation": "Firewall Policy Violation (NAT Series)", "license_limitation_reached": "Maximum license limit reached.", "license_not_enough": "Node limit exceeded, please delete some nodes or upgrade your MXview One license", "license_over": "Node limit exceeded, delete some nodes or update your MXview One license", "lldp_change": "LLDP table changed", "logging_capacity": "Event log over capacity threshold", "login_radius_fail": "Login authentication failed by the RADIUS+ server", "login_radius_success": "Login authentication is successful from the RADIUS+ server", "login_tacas_fail": "Login authentication failed by the TACACS+ server", "login_tacas_success": "Login authentication is successful from the TACACS+ server", "mac_sticky_violation": "MAC sticky violation", "mrp_multiple_event": "MRP multiple managers event occurred.", "mrp_ring_open_event": "MRP ring open event occurred.", "mstp_topology_changed": "MSTP topology changed", "mxview_autopology_finish": "Auto topology finished", "mxview_autopology_start": "Auto topology started", "mxview_db_backup_fail": "Database backup failed", "mxview_db_backup_sucess": "Database backup completed, stored in %MXviewPRO_Data%\\db_backup\\{{param1}} {{param2}}", "mxview_job_done": "Job: {{jobname}} done", "mxview_job_start": "Job: {{jobname}} start", "mxview_server_license_limit": "Not enough licenses from MXview One Central Manager", "mxview_server_start": "MXview One server started", "mxview_sms_fail": "Failed to send SMS notification", "mxview_sms_success": "All MXview One network and device events are cleared", "mxview_user_lockout": "Account {{param}} locked out temporarily", "mxview_user_login_fail": "Failed to log into MXview One.", "mxview_user_login_sucess": "User login: {{param}}", "mxview_user_logout": "User logout: {{param}}", "network_latency": "Network latency to MXview One Central Manager exceeded 100 ms", "new_port_role_selected": "New port role selected.", "new_root_bridge_selected_in_topology": "New root bridge selected in topology.", "notification_sfp_rx_below": "SFP RX below threshold", "notification_sfp_temp_over": "SFP temperature over threshold", "notification_sfp_tx_below": "SFP TX below threshold", "notification_sfp_voltage_below": "SFP voltage below threshold", "notification_sfp_voltage_over": "SFP voltage over threshold", "nport_syslog_over_threshold": "NPort is over its system log threshold", "opcua_server_start": "MXview One OPC UA server is started.", "opcua_server_stop": "MXview One OPC UA server has stopped.", "ospf_designated_router_changed": "OSPF designated router changed.", "ospf_designated_router_interface_and_adjacency_changed": "OSPF designated router interface and adjacency changed.", "out_of_memory": "Less than 20% memory available.", "output_bandwidth_over_threshold": "The output bandwidth utilization has exceeded the threshold.", "output_bandwidth_over_threshold_disabled": "No threshold setting of output bandwidth utilization for port {{portIndex}}.", "output_bandwidth_over_threshold_recovery": "Port {{portIndex}} output bandwidth utilization recovered, value is {{currentValue}}.", "output_bandwidth_over_threshold_with_port": "The output bandwidth utilization ({{currentValue}}) of port {{portIndex}} has exceeded the threshold ({{threshold}}).", "output_bandwidth_under_threshold": "The output bandwidth utilization is below the threshold.", "output_bandwidth_under_threshold_disabled": "No threshold setting for output bandwidth utilization for port {{portIndex}}.", "output_bandwidth_under_threshold_recovery": "Port {{portIndex}} output bandwidth utilization recovered, value is {{currentValue}}.", "output_bandwidth_under_threshold_with_port": "The output bandwidth utilization ({{currentValue}}) of port {{portIndex}} is below the threshold ({{threshold}}).", "output_packet_error_over_threshold": "The output packet error rate has exceeded the threshold.", "output_packet_error_over_threshold_disabled": "No threshold setting for output packet error for port {{portIndex}}.", "output_packet_error_over_threshold_recovery": "Port {{portIndex}} output packet error recovered, value is {{currentValue}}.", "output_packet_error_over_threshold_with_port": "The output packet error rate ({{currentValue}}) of port {{portIndex}} has exceeded the threshold ({{threshold}}).", "overheat_protection_now_active_for_power_module": "Overheat protection now active for power module {{ x }}.", "password_automatically_changed_failed": "Failed to automatically change the device password.", "password_automatically_changed_success": "The device password was successfully changed automatically.", "password_automation_scheduled": "Password automation has reached the scheduled time and will start executing.", "phr_port_timediff": "PHR AB Port Timediff.", "phr_port_wrong_lan": "PHR AB Port Wrong LAN.", "poe_off_info": "The device is not powered by PoE.", "poe_on_info": "The device is powered by PoE.", "port_linkdown_event": "Port {{portindex}} link down", "port_linkdown_recovery": "Port {{portindex}} link up", "port_linkup_event": "Port {{portindex}} link up", "port_linkup_recovery": "Port {{portindex}} link down", "port_loop_detect": "Port looping on port {{portnum}} has been detected", "port_loop_detect_resolved": "Port looping on port {{portnum}} has been resolved.", "port_loop_detected": "Port Looping", "port_pd_short_circuited": "Port {{portnum}} Non-PD or PD short circuited.", "port_traffic_overload": "Port {{portIndex}} traffic overload {{percent}}%", "power_danger_recovery": "Power {{param}} changed to AC", "power_has_been_cut_due_to_overheating": "Power has been cut due to overheating.", "power_module_fan_malfunction": "Power module fan malfunction.", "power_type_danger": "Power {{param}} changed to DC", "pse_fet_bad": "PoE port {{portIndex}} external FET failed", "pse_over_temp": "PSE chip over temperature", "pse_veeuvlo": "PSE chip VEE under voltage lockout", "ptp_grandmaster_changed": "PTP Grandmaster Changed", "ptp_sync_status_changed": "PTP Sync Status Changed", "rateLimit_off": "Port {{portindex}} rate limit off", "rateLimit_on": "Rate limiting is active on Port {{portindex}}.", "recorved_device_lockdown_violation": "Recovered from device lock down violation", "recorved_l3_firewall_policy_violation": "Recovered from Firewall Policy Violation (NAT Series)", "redundancy_topology_change": "Redundancy Topology changed.", "syslog_server_start": "MXview One syslog server is started.", "syslog_server_stop": "MXview One syslog server has stopped.", "system_temperature_exceeds_threshold": "System temperature exceeds threshold.", "temporary_account_activate_success": "The temporary account at {{ip}} was activated successfully.", "temporary_account_deactivate_success": "The temporary account at {{ip}} was deactivated successfully.", "thermal_sensor_component_overheat_detected": "Thermal sensor component overheat detected.", "trunk_port_link_down": "Trunking port{{portindex}} link down (Physical port:{{param}})", "trunk_port_link_down_recovery": "Trunking port {{portindex}} link up (Physical port:{{param}})", "trunk_port_link_up": "Trunking port{{portindex}} link up (Physical port:{{param}})", "trust_access_under_attack": "Device trust access policy violation", "trust_access_under_attack_recovery": "Recovered from device trust access policy violation", "turbo_ring_master_match": "Turbo Ring master matched", "turbo_ring_master_mismatch": "Turbo Ring master mismatched", "turbo_ring_master_unknow": "Turbo Ring master event, status unknown", "turbochain_topology_change": "Turbo Chain Topology changed", "turboring_coupling_port_change": "Turbo Ring Coupling Port changed", "turboring_master_change": "Turbo Ring Master changed", "unknown_device_detected": "An unknown device has been detected", "user_login_fail": "Account authentication failed", "user_login_success": "Account authentication success: {{username}}", "usercode_revoke": "User Code regenerated by user", "vpn_link_recovery": "VPN tunnel {{param}} recovered", "vpn_linkdown": "VPN tunnel {{param}} disconnected", "vpn_linkup": "VPN tunnel {{param}} connected", "vrrp_master_changed": "VRRP Master Changed", "warn_start": "Warm start"}, "event_detail_title": "Event Detail ID: {{eventId}}", "filter": "Filter Conditions", "filter_end_time": "End Date", "filter_event": "Type to filter events", "filter_event_type": "Quick filter events", "filter_from_time": "Start Date", "filter_hour": "Hour", "filter_min": "Minute", "filter_sec": "Second", "filter_type": {"last_fifty_events": "Last 50 Events", "last_twenty_events": "Last 20 Events", "unack_events": "Unacknowledged Events", "unack_last_fifty_events": "Last 50 Unacknowledged Events", "unack_last_twenty_events": "Last 20 Unacknowledged Events"}, "group": "Group", "recent_event": "Recent Events", "severity": {"any": "Any", "critical": "Critical", "information": "Information", "network_device": "Network and Device", "system_information": "System Information", "warning": "Warning"}, "show_event_detail": "Show Details", "source": {"any": "Any", "mxview": "MXview One", "security_sensing": "Security Sensing", "trap": "Trap"}, "table_title": {"ack": "Acknowledge", "ack_time": "Ack Time", "always_show": "Always show \"Recent Events\" at startup", "description": "Description", "detail_information": "Detail Information", "event_id": "ID", "event_properties": "Event Properties", "event_source": "Source", "event_time": "Time Issued", "hide_recent_event": "Hide Recent Events", "severity": "Severity", "show_recent_event": "Show Recent Events", "site_name": "Site Name", "source_ip": "Source IP"}, "tabs": {"network_device_title": "Network and Device", "security_event": "Cybersecurity Event", "security_title": "Cybersecurity", "system_title": "System "}}, "execute_cli_object": {"add_cli_object": "Add a CLI Script", "alias": "<PERSON><PERSON>", "cli_error": {"connection_failure": "Connection failure", "handshake_failure": "Handshake failure", "login_failure": "Login failure", "port_limit": "Maximum number of Port Security address entries: {{param}}", "reach_maximum_ssid": "Maximum number of SSIDs: {{param}}", "smmp_configuration_mismatch": "SNMP configuration mismatch", "ssh_not_supported": "Failed to connect to the SSH client", "unable_to_set_port": "Unable to set port {{param}}", "unknown_error": "Unknown Error"}, "cli_Script": "CLI Script", "cli_session_timeout": "CLI session timed out", "confirm_selected_devices": "Selected Devices", "description": "Description", "execute_cli_fail": "Unable to run CLI script", "execute_cli_object": "Run a CLI Script", "execute_cli_result_hint": "If you leave this screen, you can download the execution results from Saved CLI Scripts > Execution Results.", "execute_cli_results": "CLI Execution Results", "execute_cli_script": "Run CLI Script", "failed": "Failed", "finished": "Finished", "in_progress": "In Progress ...", "ip": "IP", "model": "Model", "name": "CLI Script Name", "no_cli_object_hint": "No CLI scripts found. Add a CLI script first from Saved CLI Scripts", "not_sent": "Not Sent", "result": "Result", "save_as_cli_object": "Save as a CLI Script", "save_cli_object": "Save CL<PERSON>", "select_cli_object": "Select a CLI Script", "ssh_connection_timeout": "SSH Connection Timeout", "status": "Status"}, "firmware-management": {"action": "Action", "add-task": "Add Task", "add-to-schedule": "Scheduled Upgrade", "api-message": {"add-schedule-fail": "Unable to schedule task", "add-schedule-success": "Task scheduled", "delete-schedule-fail": "Unable to delete check interval schedule", "delete-schedule-success": "Check interval schedule deleted successfully", "fm-downloaded": "Firmware file downloaded successfully", "fm-downloading": "Downloading firmware file", "fm-ready": "Firmware file ready", "get-data-failed": "Unable to retrieve data", "get-download-fm-failed": "Unable to download firmware file", "get-release-note-failed": "Unable to retrieve release notes", "get-srs-status-failed": "Unable to query Moxa Firmware Server status", "ignored-model-fail": "Unable to add model to the Ignored Models list"}, "check-Firmware-status": "Check Firmware Status", "check-interval": "Check Interval", "check-now": "Check Now", "connected": "Connected", "description": "Description", "disconnected": "Disconnected", "download-csv-report": "Download CSV Report", "download-pdf-report": "Download PDF Report", "execution-time": "Time", "firmware-upgrade-sequential": "Upgrade Firmware (Strict Sequential)", "firmware-upgrade-smart-concurrent": "Upgrade Firmware (Smart Sequential)", "ignore-report": "Ignore Report", "ignore-report-desc1": "Are you sure you want to skip downloading the report?", "ignore-report-desc2": "If you leave this page, the report will no longer be available for download.", "ignored-models": "Ignored Models", "last-update": "Last Checked", "models": "Models", "moxa-firmware-server-status": "Moxa Firmware Server Status", "no-information": "No information available", "none": "None", "offline-desc": "No information available. No previous connection to firmware update server. Make sure the device is connected to the Internet and try again.", "proceeding-firmware-upgrade": "Firmware Upgrade Status", "proceeding-upgrade-result": "Firmware Upgrade Result", "release-note": "Release Notes", "repeat-execution": "Repeat", "retry-Failed-devices": "Retry Failed Devices", "select-devices": "Select Devices", "select-firmware": "Select Firmware", "set-upgrade-sequence": "Upgrade Sequence", "sign-here": "Signature", "start-date": "Date", "status": {"failed": "Failed", "finished": "Finished", "in-progress": "In progress", "waiting": "Waiting"}, "table-header": {"alias": "<PERSON><PERSON>", "current-version": "Current Version", "device-status": "Device Status", "ip": "IP", "latest-version-on-firmware-server": "Latest Version on the Firmware Server", "model-series": "Model Series", "order": "Order", "selected-firmware-ready": "Selected Firmware Download Status", "selected-version": "Selected Version", "status": "Status"}, "task-name": "Task Name", "title": "Firmware Management", "turn-off-check-interval": "Disable Check Interval", "turn-on-check-interval": "Enable Check Interval", "unable-to-download-firmware": "Failed to download firmware", "update-mode": "Update Mode", "upgrade-desc1": "Unable to determine the update sequence. Please add the computer running MXview One to the topology first.", "upgrade-desc2": "The current setup only supports concurrent device firmware upgrades. To use the Strict Sequential or Smart Sequential upgrade methods, you must add the computer running MXview One to the topology first.", "upgrade-firmware-report": "Upgrade Firmware Report", "upgrade-now": "Upgrade Now", "upgrade-state-desc": "The firmware upgrade may take some time. Please wait for the upgrade process to complete. ", "version": "Version"}, "general": {"common": {"action": "Action", "allow": "Allow", "any": "Any", "deny": "<PERSON><PERSON>", "description": "Description", "deviceInUse": "Device In Use", "deviceName": "Device Name", "disabled": "Disabled", "enabled": "Enabled", "endDate": "End Date", "filters": "Filters", "firmwareVersion": "Firmware Version", "group": "Group", "index": "Index", "ipAddress": "IP Address", "location": "Location", "mac": "MAC Address", "name": "Name", "online": "Online", "options": "Options", "productModel": "Product Model", "profileInUse": "Profile In Use", "refCount": "References", "serialNumber": "Serial Number", "startDate": "Start Date", "status": "Status", "title": "Panel Name"}, "dialog": {"deleteMsg": "Are you sure you want to delete the selected {{ item }}?", "deleteTitle": "Delete {{ item }}", "isSelected": "{{ number }} item(s) selected", "title_system_message": "System Message", "unsaved_hint_content": "Are you sure you want to leave this page?\nAny changes you made will not be saved.", "unsaved_hint_title": "Leave Without Saving", "warning": "Warning"}, "fileDrop": {"browse": "browse", "dropText": "Drag and drop a file here, or"}, "item_selected": "{{ number }} item(s) selected", "log": {"localStorage": "Local Storage", "logDestination": "Log Destination", "snmpTrapServer": "SNMP Trap Server", "syslogServer": "Syslog Server", "title": "Event Log"}, "menu": {"jump_page_placeholder": "Press Alt+J to Jump to a page"}, "page_state": {"application_error": "Application Error :(", "application_error_desc": "An error occurred while processing this request.", "back_link": "Back to Index Page", "page_not_found": "Page Not Found :(", "page_not_found_desc": "The requested URL was not found on this server."}, "severity": {"alert": "<PERSON><PERSON>", "critical": "Critical", "debug": "Debug", "emergency": "Emergency", "error": "Error", "high": "High", "information": "Information", "informational": "Informational", "low": "Low", "medium": "Medium", "notice": "Notice", "title": "Severity", "warning": "Warning"}, "shortWeekday": {"fri": "Fri.", "mon": "Mon.", "sat": "Sat.", "sun": "Sun.", "thu": "<PERSON><PERSON>.", "tue": "<PERSON><PERSON>.", "wed": "Wed."}, "table": {"add": "Add", "delete": "Delete", "download": "Download", "downloadAllLogs": "Download All Logs", "edit": "Edit", "filter": "Filter", "info": "Info", "more": "More", "permissionDenied": "Permission Denied", "reboot": "Reboot", "refresh": "Refresh", "reorderFinish": "Finish Reordering", "reorderPriority": "Reorder Priorities", "search": "Search", "transfer": "Transfer", "upgrade": "Upgrade"}, "top_nav": {"api_doc": {"title": "API Reference"}, "hide_recent_event": "Hide Navigation Menus", "notifications": {"message_content": "Event Content", "message_readall": "More notifications", "message_title": "Event Title", "notification_header": "Notifications"}, "show_recent_event": "Show Navigation Menus", "user_profile": {"advanced_mode": "Advanced Mode", "change_pwd": "Change Password", "greeting": "Hi", "logout": "Log Out", "manage_account": "Manage Account", "reset_factory_default": "Reset Factory Default", "restart_machine": "Restart Machine", "search": "Type keyword to search"}}, "topNav": {"caseInsensitive": "Case-insensitive", "changePwd": "Change Password", "changeSuccess": "Your password was updated successfully. Please log in again.", "confirmNewPwd": "Confirm New Password", "currentPwd": "Current Password", "invalidKey": "The following name(s) are reserved: admin, operator, viewer, root, administrator, auditor", "logout": "Log Out", "logoutMsg": "Are you sure you want to log out?", "newPwd": "New Password", "subject": "Subject", "troubleshoot": "Troubleshooting", "troubleshootMsg": "You can export the debug logs to the local host for troubleshooting.", "updateAuthority": "Update Authority", "updateSuccess": "Your account authority has been changed. Please log in again.", "username": "Username"}, "unit": {"days": "day(s)", "entries": "entries", "minute": "minute", "minutes": "minute(s)", "months": "month(s)", "percent": "%", "pkts": "pkt/s", "sec": "sec.", "seconds": "second(s)", "thousand": "thousand"}, "weekday": {"friday": "Friday", "monday": "Monday", "saturday": "Saturday", "sunday": "Sunday", "thursday": "Thursday", "tuesday": "Tuesday", "wednesday": "Wednesday"}}, "GLOBAL_MESSAGE": {"update_fail": "Failed to update", "update_success": "Updated successfully"}, "GROUP_PROPERTIES": {"description": "Description", "devices": "Devices (Normal / Warning / Critical)", "information": "Information", "name": "Name", "title": "Group Properties"}, "IMAGE": {"deviceSizeError": "The maximum image size is 100 KB.", "error": "MXview One only supports jpg, gif, and png formats", "sizeError": "The maximum image size is 1 MB"}, "inventory_management": {"active": "<PERSON><PERSON>", "alias": "<PERSON><PERSON>", "assets_list": "Asset List", "available": "Available", "channel_extended_end_date": "Channel Extended Warranty End Date", "channel_extended_warranty_end_date_hint": "If you have an extended warranty agreement with your Moxa channel provider, manually enter the extended expiration date here.", "check_warranty_manually": "Manually Check Warranty", "check_warranty_status": "Check Warranty Status", "days": "Days before", "email_example": "<EMAIL>", "email_to": "Email to", "expire_soon": "Expires soon", "expired": "Expired", "firmware_version": "Firmware Version", "invalid_email_desc": "Invalid email address", "ip": "IP", "last_update": "Last Update", "mac_address": "MAC Address", "model": "Model", "multiple_email_hint": "You can add multiple recipient email addresses, separated by a comma.", "no_data": "N/A", "notify_before": "Send Reminder", "retrieve_data": "Retrieve Data", "select": "Select", "serial_number": "Serial Number", "type": "Search by", "unable_query_warranty_server_status": "Unable to reach the Moxa warranty server.", "unable_retrieve_warranty_information": "Unable to retrieve warranty information.", "unavailable": "Unavailable", "warranty_end_date": "Warranty End Date", "warranty_end_date_notification": "Warranty Expiration Notifications", "warranty_management": "Warranty Management", "warranty_notification": "Warranty Notifications", "warranty_period": "Warranty Duration", "warranty_server_status": "Moxa Warranty Server Status", "warranty_start_date": "Warranty Start Date", "warranty_status": "Warranty Status"}, "INVENTORY_REPORT": {"alias": "<PERSON><PERSON>", "filter": "Type to filter inventory reports", "fw_version": "Firmware Version", "ip_address": "IP Address", "mac": "MAC Address", "model": "Model", "property": "Property", "report_generate_day": "Report Generation Date: ", "site_name": "Site Name", "system_desc": "System Description", "title": "Inventory Report", "value": "Value"}, "IP_CONFIGURATION": {"auto_ip": "Auto IP", "change_ip_fail": "Failed to update IP configuration", "change_ip_success": "Device IP configuration updated successfully", "gateway": "Gateway", "hint": "This feature is not available for Layer 3 devices.", "ip_address": "IP Address", "netmask": "Netmask", "title": "IP Configuration"}, "ip_conflict_detected_notification": "IP conflict detected", "ip_conflict_recovery_notification": "IP conflict resolved", "ips_configuration": {"dialog-title": "IPS Configuration", "execute_fail": "Failed to apply configuration.", "input-ips": "IPS", "option-detection-mode": "Detection Mode", "option-prevention-mode": "Prevention Mode", "selet-ips-operation-mode": "IPS Operation Mode", "th-execution-status": "Execution Status"}, "IPSEC": {"connection_name": "Connection Name", "ipsec_status_phase1": "IPSec Status Phase 1", "ipsec_status_phase2": "IPSec Status Phase 2", "local_gateway": "Local Gateway", "local_subnet": "Local Subnet", "remote_gateway": "Remote Gateway", "remote_subnet": "Remote Subnet"}, "IW": {"Message": {"CONNECT_TIMEOUT": "Connection timed out", "ERROR_OCCURRED": "Server error. Please wait and try again.", "FAILED": "Failed", "LOAD_DATA_FAILED": "Error loading data", "RSSI_SNR_ONLY": "(Signal Strength and SNR only)", "SET_SUCCESS": "Settings changed successfully", "SUCCESSED": "Succeeded", "UPDATE_FAILED": "Data update failure"}, "Title": {"ap": "AP", "AUTO_REFREASH": "Auto Refresh: ", "AUTO_REFRESH": "Auto Refresh", "BSSID": "BSSID", "channel": "Channel", "client": "Client", "client_count": "Client Count", "client_router": "Client-Router", "CLOSE": "Close", "COLOR": "Color", "COLUMN": "Parameter Column", "CONDITIONS": "Condition", "CONN_TIME": "Connection Time (sec)", "connected": "Connected", "DEVICE_NAME": "Device Name", "disable": "Disable", "ENABLE": "Enable", "FILTER_TABLE_VIEW": "Filter Table View", "hint": "This page will be removed in a future product release. Please use the Wireless Add-on in MXview One instead.", "IP_ADDR": "IP Address", "link_speed": "Link Speed", "MAC": "MAC Address", "master": "Master", "MODULATION": "Modulation", "noise_floor": "Noise Floor", "noise_floor_unit": "Noise Floor (dBm)", "OK": "OK", "ONLINE": "Online", "operation_mode": "Operation Mode", "RSSI": "Signal Strength (dBm)", "security_mode": "Security Mode", "signal_level": "Signal Level", "slave": "Slave", "SNR": "SNR (dB)", "SNR_A": "SNR-A (dB)", "SNR_B": "SNR-B (dB)", "ssid": "SSID", "TOTAL_AP": "Number of APs: ", "TOTAL_CLIENT": "Number of Clients: ", "tx_power": "TX Power", "tx_power_unit": "TX Power (dBm)", "tx_rate": "TX Rate", "tx_rate_unit": "TX Rate (Mb/s)", "uptime": "Uptime", "VALUE": "Value", "WIRELESS_TABLE_VIEW": "Wireless Table View"}}, "JOB_SCHEDULER": {"add_failed": "Failed to add job", "add_success": "Job added successfully", "add_title": "Add Job", "alias": "<PERSON><PERSON>", "auto_topology": "Auto Topology", "cli_object_name": "CLI Script Name", "config_file": "Configuration File", "config_file_error": "MXview One only supports .ini files", "config_file_size_error": "The maximum file size is 1 MB.", "current_filename": "Current Filename", "current_version": "Current Version", "daily": "Daily", "database_backup": "Database Backup", "delete_failed": "Failed to delete job", "delete_success": "Job deleted successfully", "description": "Description", "edit_title": "Edit Job", "excute_cli_object": "<PERSON> Saved <PERSON>", "execution_time": "Execution Time", "export_configuration": "Export Configuration", "filter": "Type to filter jobs", "fm_sequential": "Strict Sequential", "fm_smart": "Smart Sequential", "friday": "Friday", "import_configuration": "Import Configuration", "ip": "IP", "job_action": "Action", "job_log": "Job Log", "job_name": "Job Name", "model_series": "Model Series", "monday": "Monday", "monthly": "Monthly", "on": "On", "once": "Once", "order": "Order", "registered_devices": "Registered Devices", "repeat_execution": "Repeat Execution", "saturday": "Saturday", "schedule_time": "Scheduled Time", "selected_version": "Selected Version", "show_log_fail": "There is no log yet", "show_log_not_found": "No logs available", "start_date": "Start Date", "sunday": "Sunday", "thursday": "Thursday", "title": "Maintenance Scheduler", "tuesday": "Tuesday", "update_failed": "Failed to update job", "update_success": "Job updated successfully", "wednesday": "Wednesday", "weekly": "Weekly"}, "LANG": {"de-DE": "De<PERSON>ch", "en-US": "English", "es-ES": "Español", "fr-FR": "Français", "ja-JP": "日本語", "ru-RU": "Русский язык", "zh-CN": "中文(简体)", "zh-TW": "中文(繁體)"}, "LICENSE": {"activation_code": "Activation Code", "activation_code_error": "Invalid activation code", "activation_title": "Activation", "active": "Activate", "active_desc_1": "Visit", "active_desc_2": "http://license.moxa.com/", "active_desc_3": "and fill in your Product Code and User Code to get your activation code.", "add_fail": "Failed to add license", "add_license": "Add License", "add_new_license_desc": "You can add a license here.", "add_new_license": {"activate": "Activate", "activate_intro_link": "Moxa License Site", "activate_intro_pre": "Download the license from the", "activate_intro_suf": ", and paste the Activation Code here.", "copy_user_code": "Copy User Code", "copy_user_code_intro_link": "Moxa License Site", "copy_user_code_intro_pre": "Copy the User Code to the", "copy_user_code_intro_suf": " ", "license_site_step_1_link": "Moxa License Site", "license_site_step_1_pre": "1. Log in to the", "license_site_step_1_suf": " ", "license_site_step_2": "2. Choose \"Activate a Product License\" and \"MXview One\" on the site.", "license_site_step_3": "3. Registration Code", "license_site_step_3_Free_step": "Go to the next step.", "license_site_step_3_Free_title": "Free Version Users: ", "license_site_step_3_Full_step": "Enter the Registration Code and User Code on the Moxa License Site. A User Code will be provided in a later step.", "license_site_step_3_Full_title": "Full License Users: ", "login_license_site": "Log in to the Moxa License Site", "select_network_adapter": "Select Network Adapter", "title": "Add New License"}, "add_success": "License added successfully", "copied_to_clipboard": "Copied to clipboard", "copy_deactivation_code": "Copy Deactivation Code", "copy_older_license_code": "Copy 2.x license code", "current_nodes": "Currently Used:", "deactivate": "Deactivate", "deactivate_fail": "Failed to deactivate license", "deactivate_success": "License deactivated successfully", "deactivated_licenses": "Deactivated licenses", "deactivating": "Deactivating...", "deactivation_code": "Deactivation Code", "deactivation_desc": "The license will be invalid after deactivation, are you sure you want to deactivate the license?", "deactivation_title": "Deactivation", "disabled": "Disabled", "duration": "Duration", "enabled": "Enabled", "expired_license": "Expired Licenses", "free_trial": "Free Trial", "free_trial_desc": "Start to experience the power of MXview One", "import_license_file": "Import License File", "ips": "IPS", "license": "License:", "license_authorized": "Authorized", "license_free": "Free license", "license_none": "None", "license_site": "Moxa License Site", "license_start": "License Start", "license_title": "License", "license_trial": "Trial", "license_type": {"node_base_intro": "Specifies the number of the devices that MXview One can monitor in the network.", "node_base_title": "Node License", "power_addon": "Power Add-on License", "power_intro": "Allows users to access additional power related functions.", "security_addon": "Security Add-on License", "security_intro": "Allows users to access additional security related functions.", "title": "License Type", "trial_intro": "You can experience the power of MXview One for 90 days.", "trial_title": "Trial License", "wireless_addon": "Wireless Add-on License", "wireless_intro": "Allows users to access additional wireless related functions."}, "licensed_node": "Licensed Node", "licensed_nodes": "Total Number of Licenses:", "licenses": "Licenses", "managed_by_central": "The License is managed by MXview One Central.", "managed_by_central_licenses_invalidated": "No valid licenses, please check the status in Control Panel.", "mxview": "MXview One", "network_adapter": {"button": "Select Network Adapter", "change_network_adapter": "Change Network Adapter", "change_network_adapter_alert_1": "Are you sure you want to change the network adapter?", "change_network_adapter_alert_2": "All the licenses will be deactivated once you click \"Confirm\". You will be unable to use MXview One until you register the new license with a new network adapter.", "intro": "MXview One binds the license to a network adapter. Please choose the adapter you want to bind the license to. Re-selecting a network adapter will deactivate all your licenses automatically, and you will have to register them again.", "select_adapters": "Select Adapters", "select_adapters_desc": "Please select a network adapter, MXview One will use it to generate your User Code.", "title": "Network Adapter"}, "node": "Current Nodes / Licensed Nodes:", "nodes": "nodes", "older_license": "2.x License", "older_license_nodes": "2.x nodes", "over_nodes_desc": "You were logged out because the number of monitored nodes is more than what your license supports.", "over_nodes_title": "Warning", "power_addon_trial": "Start to experience the MXview One Power Add-on", "reactivate_license": {"activate": "Activate", "activate_intro_link": "Moxa License Site", "activate_intro_pre": "Download the license from ", "activate_intro_suf": ", and paste the Activation Code here.", "copy_deactivate_code": "Copy Deactivation Code", "copy_deactivate_code_intro_link": "Moxa License Site", "copy_deactivate_code_intro_pre": "Copy Deactivation Code and paste it on ", "copy_deactivate_code_intro_suf": " ", "copy_user_code": "Copy User Code", "copy_user_code_intro_link": "Moxa License Site", "copy_user_code_intro_pre": "Copy the User Code to ", "copy_user_code_intro_suf": " ", "intro": "Use both the Deactivation Code and a User Code to re-activate your license.", "license_site_step_1_link": "Moxa License Site", "license_site_step_1_pre": "1. Log in to the", "license_site_step_1_suf": " ", "license_site_step_2": "2. Click \"Transfer a Product License\" and \"Transfer to another Device\" on the site.", "license_site_step_3": "3. Select MXview One for the Software Product.", "login_license_site": "Log In to the Moxa License Site", "title": "Re-activate License", "title_abbr": "Re-activate"}, "reason": "State", "relaunch": {"activating": "Activating...", "active_note": "The operation will finish in {{timer}} seconds."}, "remain": "<PERSON><PERSON><PERSON>", "security_addon_trial": "Start to experience the MXview One Security Add-on", "select_network_interface": "Select Network Interface", "site_license_invalid": "Some sites have invalid licenses", "site_license_invalid_title": "License Invalid", "start_free_trial": "Start Trial", "start_free_trial_fail": "Failed to start free trial", "start_free_trial_success": "Free trial started successfully", "state": "State:", "state_all_licenses_invalidated": "No valid licenses", "state_cannot_add_free_license": "Cannot add a free license when you have full licenses", "state_cannot_add_multiple_free_licenses": "Cannot add multiple free licenses", "state_format_incorrect": "The format of the license file is incorrect", "state_general_error": "General error", "state_license_deactivated": "License already deactivated", "state_license_expired": "License expired", "state_license_is_registered": "License already registered in the system", "state_license_not_found": "License not found", "state_license_over_2000": "Your license has exceeded 2000 nodes, which is the maximum amount available for MXview One.", "state_license_upgrade_error": "Unable to add the upgrade license. MXview One needs at least one full license.", "state_license_upgrade_no_full_license ": "Unable to remove the license. You must remove all upgrade licenses first.", "state_no_full_license": "No valid MXview One license", "state_no_usercode": "No User Code", "state_over_nodes": "Not enough nodes for the amount of devices you want to deploy, please purchase additional nodes", "state_trial_expired": "Trial expired", "state_trial_is_began": "Trial already started", "state_trial_not_activated": "Trial not activated yet", "state_usercode_deactivated": "User Code deactivated", "state_usercode_exists": "User Code already exists", "state_usercode_not_match": "The User Code in the license does not match the User Code of the system", "state_usercode_not_match_adapter": "The User Code bound to this license was not found", "title": "License Management", "trial_button": "TRIAL", "trial_day": "days", "trial_expired": "Trial Expired", "trial_over_nodes": "MXview One will be locked after 30 minutes if you do not add enough licenses or remove nodes over the usage limit.", "trial_remaining": "Trial Remaining", "user_code": "User Code:", "valid": "<PERSON><PERSON>", "wireless_addon_trial": "Start to experience the MXview One Wireless Add-on"}, "link_list": {"rx": "RX (%)", "tx": "TX (%)"}, "LINK_PROPERTIES": "Link Properties", "LOGIN": {"account_reach_limit": "The limit for simultaneous logins has been reached (10)", "all_sites_offline": "All sites are offline", "default_password_warning": "Please change the default password to ensure security", "error": "Invalid username or password", "ie_not_supported": "MXview One does not support IE, please use Google Chrome for the best experience", "last_login_fail": "The latest login failure record(s)", "last_login_succeed": "The latest successful login time was", "login_fail_time": "{{loginTime}} from {{loginIp}}", "login_succeed_time": "{{loginTime}} from {{loginIp}}", "logout": "Log Out", "password": "Password", "password_policy_mismatch": "Password does not match the password policy", "sign_in": "Log In", "username": "Username", "welcome": "Welcome"}, "max_char": "Maximum {{num}} characters", "min_char": "Minimum {{num}} characters", "model-port-mapping": {"port": "Port", "web-ui": "On-Device Web UI"}, "MXVIEW_WIZARD": {"complete_page_title": "Complete", "navigate_to_wizard_page": "Do you want to use the MXview One Setup Wizard?", "step_add_scan_range": "Add Scan Range", "step_auto_topology": "Draw Topology (for devices that support LLDP)", "step_create_group": "Create Group", "step_select_site": "Select a Site to Set Up", "step_set_snmp": "Set SNMP Settings", "step_set_trap_server": "Set SNMP Trap Server", "title": "Setup Wizard", "welcom_page_title": "Welcome to the Setup Wizard"}, "NETWORK_MENU": {"add_link": "Add Link", "add_wifi_ssid": "Add Wi-Fi SSID", "alignment": {"bottom": "Bottom Align", "left": "Left Align", "right": "Right Align", "title": "Alignment", "top": "Top Align"}, "copy_device_list": "Copy Device List", "create_a_snapshot": "Create Snapshot", "cybersecurity_control": "Cybersecurity Controls", "delete": "Delete", "device_configuration": "Device Configuration", "device_control": "Device Control", "device_dashboard": "<PERSON>ce Dashboard", "device_login_account": "<PERSON>ce Accounts", "device_panel": "Device Panel", "device_wireless_settings": "Per-device Parameters", "disable_unsecured_http_and_telnet_console": "Disable Insecure HTTP and Telnet Console", "disable_unused_ethernet_and_fiber_ports": "Disable Unused Ethernet and Fiber Ports", "document": {"menu": {"open": "Open", "set": "Set"}, "title": "Document"}, "dynamic_mac_sticky": "Dynamic Sticky MAC", "edit": {"menu": {"add_device": "Add <PERSON>", "delete_background": "Delete Background", "export_device_list": "Export Device List", "export_topology": "Export Topology", "import_device_list": "Import Device List", "set_background": "Set Background"}, "title": "Edit"}, "execute_cli": {"menu": {"execute_cli_object": "<PERSON> Saved <PERSON>", "execute_cli_script": "Run CLI Script"}, "title": "<PERSON>"}, "grid": {"menu": {"import_scd": "Import SCD"}, "title": "Power"}, "group": {"menu": {"change_group": "Change Group", "create_group": "Create Group", "group_maintenance": "Group Maintenance"}, "title": "Group"}, "grouping": "Grouping", "ips_configuration": "IPS Configuration", "ipsec_status": "IPsec Status", "link_traffic": {"menu": {"packet_error_rate": "Packet Error Rate", "port_traffic": "Port Traffic"}, "title": "Link Traffic"}, "locator": "Locator", "mac_sticky_on_off": "Sticky MAC On / Off", "maintenance": {"menu": {"advance_settings": "Advanced Settings", "assign_model": "Assign Model", "basic_information": "Basic Information", "change_device_icon": "Change Device Icon", "device_identification_settings": "Device Identification Settings", "eip_enable": "EtherNet/IP Enable", "eip_tcp_port": "EtherNet/IP TCP Port", "eip_udp_port": "EtherNet/IP UDP Port", "export_config": "Export Config", "generate_qr_code": "Generate QR Code", "import_config": "Import Config", "ip_configuration": "IP Configuration", "modbus_enable": "Modbus Enable", "modbus_port": "Modbus Port", "modbus_tcp_configuration": "Modbus TCP Settings", "polling_ip_setting": "Polling IP Settings", "polling_settings": "MXview One Polling Interval", "port_settings": "Ethernet/Fiber Port Settings", "s7_port": "Siemens S7comm Port", "s7_status": "Siemens S7comm Enable", "serial_port_monitoring": "Serial Port Monitoring", "snmp_settings": "SNMP Communication Protocol", "trap_server": "Trap Server", "upgrade_firmware": "Upgrade Firmware"}, "title": "Maintenance"}, "modify_device_alias": "<PERSON><PERSON>", "policy_profile_deployment": "Policy Profile Deployment", "reboot": "Reboot", "refresh": "Refresh", "restart_mac_sticky_learning": "Relearn Dynamic Sticky MAC", "restore_to_created_snapshot": "<PERSON>ore from Snapshot", "scale": "Scale", "security_package_deployment": "Security Package Deployment", "set_port_label": "Set Port Label", "severity_threshold": "Severity Threshold", "sfp": "SFP", "sfp_Info": "SFP Info", "sfp_list": "SFP List", "sfp_sync": "Sync Threshold From the Device", "tools": {"menu": {"device_panel": "Device Panel", "mib_browser": "MI<PERSON> Browser", "ping": "<PERSON>", "telnet": "Telnet", "web_console": "Web Console"}, "title": "Tools"}, "topology": {"menu": {"auto_layout": "Auto Layout", "auto_topology": "Auto Topology", "embed": "<PERSON><PERSON> Widget", "scan_range": "<PERSON>ce Discovery"}, "title": "Topology"}, "ungrouping": "Ungrouping", "upgrade_patch": "Apply System Update", "visualization": {"menu": {"igmp": "IGMP", "security_view": "Security View", "traffic_view": "Traffic View", "vlan": "VLAN", "vlan_view": "VLAN View"}, "title": "Visualization"}, "wifi_channel_change": "Change Wi-Fi Channel", "wireless_settings": "General Parameters", "wireless": {"menu": {"wireless_planner_view": "Wireless Coverage", "wireless_playback_view": "Wireless Roaming Playback", "wireless_table_view": "Wireless Device Summary"}, "title": "Wireless"}}, "NETWORK": {"current_status": {"no_event": "No events", "title": "Current Status", "v3_trap_event_clear_fail": "Failed to clear V3 Trap events", "v3_trap_event_suggestion": "Plase check SNMP v3 configuration"}, "not_selected": "Select a module to show device details"}, "NORTHBOUND_INTERFACE": {"custom_opc": {"add_custom_opc": "Add custom OPC tags", "all": "All", "apply_fail": "Failed to add custom OPC tags", "apply_success": "New custom OPC tags added successfully", "delete_fail": "Failed to delete the OPC tag", "delete_success": "OPC tag deleted successfully", "device_properties": "Device Properties", "enable": "Enabled Custom OPC Tags", "filter_custom_opc": "Type to filter custom OPC tags", "get_fail": "Failed to get custom OPC tags", "property_name": "Property Name", "register_devices": "Registered Devices", "title": "Custom OPC Tags", "update_custom_opc": "Update custom OPC tags", "update_fail": "Failed to update the OPC tag", "update_success": "Custom OPC tags updated successfully"}}, "NOTIFICATION_SETTINGS": {"action": "Notification Output", "action_cant_deleted": "This action is being used by one or more notifications. Please remove this action from all notifications then try again.", "action_information": "Information", "action_name": "Name", "action_tab_hint": "Please go to the Notification Output tab and add an action first", "action_type": "Type", "add_action": "Add Notification Output", "add_action_fail": "Failed to add action", "add_action_success": "New action added successfully", "add_notification": "Create Notification", "add_notification_fail": "Failed to add notification", "add_notification_success": "New notification added successfully", "check_security_tab": "Check the cybersecurity tab", "content": "Content", "delete_action_fail": "Failed to delete action", "delete_action_success": "Action deleted successfully", "delete_notification_fail": "Failed to delete notification", "delete_notification_success": "Notification deleted successfully", "edit_action": "Edit Notification Output", "edit_notification": "Edit Notification", "email": "Email", "email_content_hint": "The content here will be appended to the body of the default notification mail", "event_type": "Event", "file_size_error": "The maximum file size is 1 MB", "file_type_error": "MXview One only supports .wav files", "filter_action": "Type to filter actions", "filter_notification": "Type to filter notifications", "messagebox": "Message Box", "mobile": "MXview ToGo", "mobile_number": "Mobile Number", "notification": "Notification Setting", "notification_name": "Notification Name", "notification_name_exist": "This name is already used by another notification", "receiver_email": "Receiver <PERSON><PERSON>", "register_devices": "Registered Devices", "register_subscribers": "Registered Outputs", "slack": "<PERSON><PERSON>ck", "sms": "SMS", "snmptrap": "SNMP Trap", "sound": "Sound File", "teams": "Microsoft Teams", "testConnection": "Test Connection", "title": "Notification Settings", "update_action_fail": "Failed to update action", "update_action_success": "Action updated successfully", "update_notification_fail": "Failed to update notification", "update_notification_success": "Notification updated successfully", "webhook": "Webhook", "webhook_fail": "Failed to send webhook", "webhook_success": "Webhook sent successfully"}, "OPC_UA_SERVER": {"add_opc_tags": "Add OPC Tag", "anonymous": "Anonymous", "auth_setting": "Authentication Settings", "certificate": "Certificate", "certificate_link": "Download and manage certificates from the", "change_authentication_password": "Change Authentication Password", "change_password": "Change Password", "control_panel": "MXview One Control Panel", "create_tags_fail": "Failed to add tag", "create_tags_success": "Tag created successfully", "delete_tag_content": "Are you sure you want to delete this OPC tag?", "delete_tags": "Delete OPC Tag", "delete_tags_content": "Are you sure you want to delete these OPC tags?", "delete_tags_fail": "Failed to delete tags", "delete_tags_success": "Tags deleted successfully", "device_property": "Device Property", "disabled": "Disabled", "edit_opc_tags": "Edit OPC Tag", "edit_tags_fail": "Failed to update tag", "edit_tags_success": "Tag updated successfully.", "enable_opc_server": "Enable OPC UA Server", "enabled": "Enabled", "exceed_server_performance": "Maximum number of registered devices (4000) reached.", "get_tags_list_fail": "Failed to retrieve tags list", "ip_domain_name": "IP/Domain Name", "method": "Method", "opc_tags": "OPC Tags", "property_name": "Device Property", "registered_device": "Registered Devices", "security": "Security Mode", "security_placeholder": "Allow No Security", "server_settings": "Server Settings", "status": "Status", "support_security_policy": "Supported Security Policies", "tag_name": "Tag Name", "tag_name_duplicate": "This tag name already exists", "tags_exceed_limit": "Maximum number of tags (2000) reached.", "title": "OPC UA Server", "update_fail": "Failed to update settings", "update_server_setting_fail": "Failed to update server settings", "update_server_setting_fail_no_up": "Failed to update settings. The specified IP does not exist.", "update_server_setting_success": "Server settings updated successfully", "username": "Account and Password"}, "PAGES_MENU": {"about": "About", "administration": {"account_management": "Account Management", "device_settings_template": "Default Device Template", "global_device_settings": "Global Device Settings", "license_management": "License Management", "maintenance_scheduler": "Maintenance Scheduler", "preferences": "Preferences", "system_settings": "System Settings", "title": "Administration", "troubleshooting": "Troubleshooting"}, "alert": {"custom_events": "Custom Event", "device_threshold": "<PERSON><PERSON>", "event_settings": "Event Settings", "link_threshold": "<PERSON>", "notifications": "Notification Management", "title": "<PERSON><PERSON>"}, "cli_object_database": {"title": "Saved C<PERSON><PERSON>"}, "dashboard": "Dashboard", "device_management": {"account_password": "Account and Password", "configuration_control": "Configuration and Control", "title": "Device Management"}, "devices": {"device_configurations": "Device Configurations", "list_of_devices": "List of Devices"}, "event": {"all_events": "Event History", "custom_events_management": "Custom Event", "notification_management": "Notification Management", "syslog_settings": "Syslog Settings", "syslog_viewer": "Syslog Viewer", "title": "Event Management"}, "firewall_policy_management": {"dos_descr": "Configure DoS Policy", "ips_descr": "Configure IPS Policy", "layer3to7_descr": "Configure Layer 3-7 Firewall Policy", "policy_profile_deployment": "Policy Profile Deployment", "policy_profile_management": "Policy Profile Management", "security_package_deployment": "Security Package Deployment", "security_package_management": "Security Package Management", "sessionControl_descr": "Configure Session Control Policy", "title": "Firewall Policy Management"}, "firmware_management": {"title": "Firmware Management"}, "help": {"about_mxview": "About MXview One", "api_documentation": "API Documentation", "title": "Help", "user_manual": "User Manual"}, "license": "License", "links": {"list_of_rj45_links": "List of RJ45 Links", "list_of_sfp_links": "List of SFP Links", "list_of_wifi_links": "List of Wi-Fi Links", "title": "Links"}, "migrations": {"configuration_center": "Device Configuration Center", "database_backup": "Database Backup", "job_scheduler": "Maintenance Scheduler", "title": "Migrations"}, "network": {"scan_range": "Scan Range", "title": "Network", "topology": "Topology", "wizard": "<PERSON>"}, "northbound_interface": {"custom_opc_tags": "Custom OPC Tags", "opc_ua_server": "OPC UA Server", "restful_api_management": "API Key Management", "title": "Integration", "web_widget_embedded": "Embedded Web Widget"}, "preferences": "Preferences", "report": {"assets_and_warranty": "Assets and Warranty", "availability_report": "Availability Report", "inventory_report": "Inventory Management", "rogue_device_detection": "Rogue Device Detection", "title": "Reports", "vlan": "VLAN Report"}, "scan_range_wizard": {"title": "<PERSON>ce Discovery"}, "security": {"account_management": "Account Management", "security_analyser": "Security Analyzer", "title": "Security"}}, "pages": {"license": {"ips_tab": {"activate": "Activate", "activatedCode": "Activation code", "activatedTime": "Activate Time", "activateIntroPre": "Download the license from the ", "activateIntroSuf": ", and paste the Activation Code here.", "activationCode": "Activation Code", "addLicenseStep1": "Log in to the ", "addLicenseStep2": "Choose \"Activate a Product License\" and select \"{{ licenseTypeName }}\" as the product type.", "addLicenseStep3": "Enter the Registration Code and UUID on the Moxa License Site. The UUID can be obtained in the next step.", "addLicenseSuccess": "License added successfully", "addNewLicense": "Add New License", "availablePoints": "IPS Point Balance", "below": "Less than", "condition": "<PERSON><PERSON>", "copyDeactivateCode": "Copy Deactivation Code", "copyId": "Copy UUID", "copyIntro": "Copy and paste the UUID into the ", "createTime": "Create Date", "customized": "Custom", "customizedDeactivated": "Custom (Deactivated)", "customizedExpired": "Custom (Expired)", "daily": "Daily", "dailyConsumePoints": "Daily Point Usage", "days": "days", "daysRemaining": "days remaining", "deactivateCode": "Deactivation Code:", "deactivated": "Deactivated", "deactivateIPSLicenses": "Deactivate IPS Licenses", "deactivateIpsLicenses": "IPS License(s) deactivated successfully", "deactivateIPSLicensesContent1": "This will deactivate all current licenses and disable IPS pattern update functionality for affected devices. The deactivated licenses can be reactivated on a new server instance.", "deactivateIPSLicensesContent2": "Are you sure you want to continue?", "deactivateLicense": "Deactivate Licenses", "deactivationCode": "Deactivation Code", "depleted": "Depleted", "deviceIsOffline": "The device is offline.", "duration": "Valid for", "emailRecipient": "Email <PERSON>cipient", "enableNetworkSecurity": "Please enable the network security package on device", "endTime": "End Date", "estimatedPointsRunOutDate": "Estimated Point Depletion Date", "expiryDate": "Expiry Date", "firmwareVersion": "Firmware Version", "frequency": "Notification Frequency", "getNewLicense": "Request New License", "group": "Group", "history": "License History", "insufficientMsg": "The {{ category }} license has less valid nodes than the MXsecurity license. Please contact the administrator to confirm the license assignment.", "ips": "IPS", "ipsMgmt": "IPS-MGMT", "ipsLicensedDevicesManagement": "IPS License Management", "ipsLicenseHaveBeenDeactivated": "No active license(s) to deactivate.", "ipsLicenseStatus": "IPS License Status", "ipsNodeErrorMsg": "The number of bound devices ({{ devices }}) exceeds the number of nodes ({{ nodes }}) on the IPS license. Please unbind any unnecessary devices.", "license_awaiting_conversion": "Licenses Awaiting Conversion", "license_transfer": "License Transfer", "licenseAwaitingConversion": "Licenses Awaiting Conversion", "licenseBinding": "Device License Binding", "licenseDuration": "License Duration", "licenseExpiryNotification": "License Expiry Notifications\n", "licenseMigrate": "License Migration", "licenseNodes": "License Nodes", "licensePoints": "License Points", "licenseRunOutNotification": "License Depletion Notifications", "licenseSite": "Moxa License Site", "licenseStatus": "License Status", "licenseType": "License Type", "location": "Location", "loginLicenseSite": "Go to the Moxa License Site", "mac": "MAC", "monthly": "Monthly", "moxaLicenseSite": "Moxa License Site", "name": "Name", "navigatorSMTP1": "Email settings are not configured. Go to ", "navigatorSMTP2": "Administration > System Settings > Email Server Configuration", "navigatorSMTP3": " to configure the required settings.", "newUUID": "New UUID (from new server instance)", "newVersionNotSupport": "Please upgrade the firmware higher than {{version}}", "node": "Total Nodes", "nodeErrorMsg": "Insufficient valid nodes on the {{ category }} license. Please contact the administrator to confirm the license assignment.", "noIpsLicense": "No IPS license.", "noIpsLicensePoints": "All licenses are already depleted.", "noLicense": "No License", "not_transfer_license": "No license to transfer", "notSupportIPS": "This product does not support IPS", "notSupportModel": "Not support model", "notTransferLicense": "No license to transfer", "nsm": "MXsecurity", "nsmNodeErrorMsg": "The number of connected devices ({{ devices }}) exceeds the number of nodes ({{ nodes }}) on the MXsecurity license. Please disconnect any unnecessary devices.", "offlineDevice": "Offline device", "oldUUID": "Old UUID (from drop-down):", "oldVersionNotSupport": "Please transfer your license", "overview": "Overview", "reActivate": "Reactivate", "reactivateIPSLicensesTitle": "Reactivate IPS Licenses on New Instance", "reactivateLicenseStep2": "Go to \"Transfer a Product License\" and select \"IPS\" as the product type.", "reactivateLicenseStep3": "Provide the following information on the Moxa License Site:", "reactivateLicenseStep4": "and paste the Activation Code into the new server instance.", "reclaimed": "Reclaimed", "registerValidIPSLicenseInfo": "One or more of the selected devices have a device-based IPS license (IPS-DEVICE) assigned to it.\nClicking REGISTER will reclaim those local IPS licenses as centralized point-based licenses that are managed through the management software.", "remarkCvIpsExpiredMsg": "IPS license expired.", "remarkCvIpsExpiringMsg": "The IPS license will expire in {{ days }} days.", "remarkExpiredMsg": "The {{ category }} license has expired. To continue using all features, enter a valid license code.", "remarkExpiringMsg": "The {{ category }} license expires in {{ days }} days. To continue using all features, enter a new license code.", "remarkIpsExpiredMsg": "IPS license points depleted.", "remarkIpsExpiringMsg": "The total points of all IPS licenses will deplete in {{ days }} days.\n", "remarkUpdateFw": "Please update product firmware to {{version}} or higher for enhanced security and license management.", "removeDeviceLicense": "Deregister as IPS-licensed Device(s)", "removeLicenseDisabled": "One or more of the selected device(s) do not have a {{ category }} license applied to it", "removeLicenseMsg": "Are you sure you want to deregister the selected device(s) as IPS-licensed devices? This will reclaim the used IPS license points and disable IPS pattern update functionality on the device(s).", "removeLicenseSuccess": "License removed successfully", "securityGroup": "Group", "serialNumber": "Serial Number", "serverId": "UUID", "setDeviceLicense": "Register as IPS-licensed Device(s)", "setLicenseDisabled": "One or more of the selected device(s) already have a {{ category }} license applied to it", "setLicenseMsg": "Are you sure you want to register the selected device(s) as IPS-licensed devices? Each device will use 1 IPS license point per day.", "setLicenseSuccess": "License applied successfully", "shouldRemoveOnline": "Licenses can only be removed from online devices", "shouldSetOnline": "Licenses can only be applied to online devices", "showAllDevices": "Show All Devices", "showSelectedDevices": "Show Selected Devices", "standard": "Standard", "standardDeactivated": "Standard (Deactivated)", "standardRunOut": "Standard (Depleted)", "startTime": "Start Date", "status": "Status", "transferLicenseStep2": "Choose \"Migrate a Product License\" and select \"MXsecurity\" as the product type.", "transferLicenseStep3": "Enter the \"UUID\" on the Moxa License Site. The UUID can be obtained in the next step.", "transferLicenseSuccess": "License transferred successfully", "transferStatus": "Transfer  Status", "trial": "Trial", "trialDeactivated": "Trial (Deactivated)", "trialDepleted": "Trial (Depleted)", "type": "Type", "unlimited": "Unlimited", "update_fw": "Please update to FW {{version}} or higher", "updateDate": "Update Date", "updatedTime": "Update Date", "updateFw": "Please update to FW {{version}} or higher", "updateNotification": "Notification updated successfully", "uploadConfigFile": "Upload a license file (.csv)", "usedNode": "Used Nodes", "valid": "<PERSON><PERSON>", "validIpsDeviceInfo": "This device uses a device-based IPS license, which you can check by going to the device's web page. To reclaim, convert, and centrally manage the license, click the Register as IPS-licensed Device(s) icon.", "weekly": "Weekly"}}, "deviceDeployment": {"alreadySentSms": "Already sent {{ smsNumber }}/{{ max }} SMS in this month", "applied": "Applied", "atLeastSelectOne": "Select at least one SMS control command", "cellularModuleDisable": "The Cellular Module is disabled, sending SMS command is not permitted.", "cellularStartConnecting": "Cellular Start Connecting", "cellularStopConnecting": "Cellular Stop Connecting", "configSync": "The configuration of the selected device(s) will be synced.", "daily": "Daily", "date": "Date", "deleteMsg": "Are you sure you want to delete the selected device(s)?", "deleteSchedule": "Delete Schedule", "deleteScheduleSuccess": "Device(s) schedule deleted successfully", "deleteSuccess": "Device(s) deleted successfully", "deleteTitle": "Delete Device(s)", "device_ip": "Device IP", "deviceConfiguration": "Device Configuration", "deviceDetail": "<PERSON><PERSON>", "deviceDisableHint": "Function is disabled from the device side", "deviceSelected": "device(s) selected", "endTime": "End Date", "firmware": "Firmware", "firmwareUpgrade": "The firmware of the selected device(s) will be upgraded.", "firmwareVersion": "Firmware Version", "general": "General", "groupName": "Group Name", "groupSelected": "group(s) selected", "invalidDate": "Invalid Date", "invalidPeriod": "Invalid Period", "lastRebootTime": "Last Reboot Time", "lastUpdate": "Last Update", "lastUpdateTime": "Last Update Time", "location": "Location", "mac": "MAC", "manually": "Manually", "maxSms": "You have reached the monthly SMS limit (MAX. {{ max }})", "noConfigAvailable": "No configurations available", "noConfigMsg": "Check the configurations on the Management/Device Configuration page.", "noFirmwareMsg": "Check the firmware files on the Management/Firmware page.", "noPackageMsg": "Check the security packages on the Security Package Management page.", "noProfileAvailable": "No profiles available", "noProfileMsg": "Check the profiles on the Policy Profile Management page.", "notSupportModel": "Not support model", "notSync": "Not Synced", "noVersionAvailable": "No version available", "oneTime": "One Time", "outOfSync": "Out of Sync", "package": "Package", "packageUpgrade": "The security package of the selected device(s) will be upgraded.", "packageVersion": "Package Version", "period": "Period", "policyProfile": "Policy Profiles", "processing": "Processing", "profileName": "Profile Name", "profileSync": "The profile of the selected device(s) will be synced.", "reboot": "The device(s) will be rebooted.", "rebootDisabled": "Only online device(s) can be rebooted.", "rebootMsg": "Are you sure you want to reboot the selected device(s)?", "rebootTitle": "Reboot Device(s)", "remoteSmsControl": "Remote SMS Control", "restoreConfigDisabled": "Only online devices of the same model type can be synced.", "sameVersionWarning": "One or more of the selected devices already have version {{ version }} applied.", "schedule": "Schedule", "scheduleDisabled": "Only devices of the same model type can be scheduled.", "scheduleOverlapMsg": "Can not select the time slot which already assigns the Reboot or Firmware upgrade.", "scheduleSettings": "Schedule Settings", "scheduling": "Scheduling", "schedulingMode": "Scheduling Mode", "schedulingPeriod": "Scheduling Period", "schedulingReboot": "Scheduling Reboot", "selectConfigFile": "Select Configuration File", "selectFile": "Select File", "sendSms": "Send SMS", "sendSmsControl": "Send SMS Control", "sendSmsOnCell": "Select one OnCell device to send SMS Control", "sendSmsSuccess": "Send SMS successfully", "serialNumber": "Serial Number", "setDoOff": "Set DO Off", "setDoOn": "Set DO On", "shouldBeSameVersion": "The package version of the selected devices should be the same.", "shouldHaveJanus": "One or more of the selected device(s) do not have a security package installed.", "shouldSyncOnline": "Only online device(s) can be synced.", "showAll": "Show All Groups and Devices", "showSelected": "Show Selected Groups and Devices", "smsCountDownHint": "Send next SMS after 60 seconds", "softwarePackage": "Security Packages", "startIpsecTunnel": "Start IPSec Tunnel", "startTime": "Start Date", "status": "Status", "statusProfileName": "Status / Profile Name", "stopIpsecTunnel": "Stop IPSec Tunnel", "switchSim": "Switch SIM", "sync": "Synced", "syncConfig": "Sync Configuration", "syncConfigTitle": "Sync Configuration To Device(s)", "syncModified": "Synced (Modified)", "syncProfile": "Sync Profile", "syncProfileTitle": "Sync Profile To Device(s)", "systemRestart": "System Restart", "time": "Time", "updateScheduleSuccess": "Device schedule updated successfully.", "upgradeDisabled": "Only online devices can be upgraded.", "upgradePackageError": "Firmware versions above 2.5.0 and below 2.4.x cannot coexist.", "upgradePackageNotSameDisabled": "Only the devices of same model type can be selected", "upToDate": "Up-to-date", "version": "Version", "weekly": "Weekly", "weeklyDay": "Weekly Day"}, "logging": {"eventLog": {"adp": "ADP", "audit": "Audit", "device": "<PERSON><PERSON>", "dos": "DoS Policy", "dpi": "Protocol Filter Policy", "endDate": "End Date", "endTime": "End Time", "event": "Event", "firewall": "Firewall", "ips": "IPS", "l2Policy": "Layer 2 Policy", "l3Policy": "Layer 3-7 Policy", "malformed": "Malformed Packets", "sc": "Session Control", "setting": "Setting", "severity": "Severity", "startDate": "Start Date", "startTime": "Start Time", "tab": {"audit": {"deviceName": "Device Name", "event": "Event", "groupName": "Group Name", "message": "Message", "severity": "Severity", "time": "Time", "username": "Username"}, "device": {"deviceName": "Device Name", "event": "Event", "groupName": "Group Name", "mac": "MAC Address", "message": "Message", "severity": "Severity", "time": "Time", "username": "Username"}, "firewall": {"action": "Action", "adp": "ADP", "all": "All", "appProtocol": "Application Protocol", "category": "Category", "deviceName": "Device Name", "dos": "DoS Policy", "dpi": "Protocol Filter Policy", "dstIp": "Destination IP", "dstMac": "Destination MAC", "dstPort": "Destination Port", "etherType": "EtherType", "event": "Event", "fromInterface": "Incoming Interface", "groupName": "Group Name", "icmpCode": "ICMP Code", "icmpType": "ICMP Type", "id": "Index", "ips": "IPS", "ipsCategory": "IPS Category", "ipsSeverity": "IPS Severity", "l3Policy": "Layer 3-7 Policy", "malformed": "Malformed Packets", "message": "Additional Message", "policyId": "Policy ID", "policyName": "Policy Name", "protocol": "IP Protocol", "security": "Security", "sessionControl": "Session Control", "severity": "Severity", "srcIp": "Source IP", "srcMac": "Source MAC", "srcPort": "Source Port", "subCategory": "Subcategory", "tcpFlag": "TCP Flags", "time": "Time", "toInterface": "Outgoing Interface", "trustAccess": "Trusted Access", "username": "Username", "vlanId": "VLAN ID"}, "vpn": {"deviceName": "Device Name", "event": "Event", "groupName": "Group Name", "message": "Additional Message", "severity": "Severity", "time": "Time", "username": "Username"}}, "trustAccess": "Trusted Access", "vpn": "VPN"}, "notification": {"advancedSettingMsg": "Once the maximum number of notifications has been reach in period of time, no more notifications are sent until next period.", "advancedSettings": "Advanced Settings", "appProtocol": "Application Protocol", "arpFlood": "ARP-Flood", "atLeastOneReceiver": "At least one Receiver", "bufferOverflow": "Buffer Overflow", "chooseDevices": "Choose Devices", "createdBy": "Created By", "createNotification": "Add Cybersecurity Event", "createSuccess": "Cybersecurity event created successfully", "deleteFailed": "This event is used for notifications and cannot be deleted.", "deleteKey": "Cybersecurity Event(s)", "deleteNotification": "Delete Notification", "deleteSuccess": "Cybersecurity event(s) deleted successfully", "deviceCount": "<PERSON><PERSON>", "deviceName": "Device Name", "DNP3": "DNP3", "dosAttacks": "DoS attacks", "dstIp": "Destination IP", "dstMac": "Destination MAC", "editNotification": "Edit Cybersecurity Event", "EIP": "EIP", "email": "Email", "emailContent": "Email Content", "emailContentDefault": "The event ${event} triggered at device ${productModel}, ${deviceName}, happened at ${eventTime}.", "emailHeader": "[MXsecurity] Notification ${notificationName}\ngenerated from ${deviceName}", "emailMsgAutoSentFrom": "This notification was automatically sent \nfrom MXsecurity.", "emailMsgCheck": "Please check the detailed information \non MXsecurity.", "emailMsgGreeting": "Dear Sir/ <PERSON>am,", "emailMsgSignOff": "Best regards,\nMXsecurity", "eq": "Equal to", "event": "Event", "event_used": "This event is already in use in the notification settings and cannot be modified.", "eventFilter": "Choose Event and Filter", "eventFilterRule": "Event Filter Rule", "eventTime": "Event Time", "exploits": "Exploits", "fileVulnerabilities": "File vulnerabilities", "filterRule": "Filter Rule", "filterRuleDetail": "Filter Rule(s) Detail", "finScan": "FIN Scan", "floodingScan": "Flooding & Scan", "GOOSE": "GOOSE", "gt": "Lower than", "gte": "Lower than or Equal to", "icmpDeath": "ICMP-Flood", "IEC-104": "IEC-104", "ipAddress": "IP Address", "ipRangeHint": "You could use * to represent the result\nof the /8/16/24 subnet mask, ex.\n192.168.*.*\nCould not be used at the beginning of\nan IP address or alone in the middle *", "ipsCate": "IPS Category", "ipSpoofing": "IP Spoofing", "ipsSeverity": "IPS Severity", "location": "Location", "lt": "Higher than", "lte": "Higher than or Equal to", "macAddress": "MAC Address", "macRangeHint": "You could use *to represent a range of\nMAC address, ex. 00:90:E8:*:*:*\nCould not be used at the beginning of a\nMAC address or alone in the middle.", "malwareTraffic": "Malware traffic", "maxEnableSize": "The MAX. enabled notifications is {{num}}.", "maxNotification": "MAX. Notification", "maxPerUserSize": "The MAX. notifications per user is {{num}}.", "MMS": "MMS", "Modbus/TCP": "Modbus/TCP", "newTcpWithoutSynScan": "TCP Sessions Without SYN", "nmapIdScan": "NMAP-ID Scan", "nmapXmasScan": "NMAP-<PERSON><PERSON>", "notificationActions": "Notification Actions", "notificationEvent": "Notification Event", "notificationInfo": "Notification Information", "notificationLimit": "Notification Limit", "notificationName": "Event Name", "nullScan": "<PERSON><PERSON>", "OmronFINS": "Omron FINS", "periodTime": "Period of Time", "policyName": "Policy Name", "productModel": "Product Model", "protocolAttackProtection": "Protocol Attack Protection", "receiverEmailAddress": "Receiver <PERSON><PERSON>dress", "receiverSetting": "Receiver Settings", "reconnaissance": "Reconnaissance", "resetToDefault": "Reset to default", "serialNumber": "Serial Number", "severity": "Severity", "severityMode": "Severity Mode", "severityRule": "Severity Rule", "showAllDevices": "Show All Devices", "showSelectedDevices": "Show Selected Devices", "srcIp": "Source IP", "srcMac": "Source MAC", "Step7Comm": "Step7Comm", "subCate": "Sub Category", "synFinScan": "SYN/FIN Scan", "synFlood": "SYN-Flood", "synRstScan": "SYN/RST Scan", "syslog": "Syslog", "syslogContent": "Syslog Content", "syslogContentDefault": "Notification ${notificationName} been triggered at device. ${productModel}, ${deviceName}, happened at ${eventTime}. Please check detail information at MXsecurity.", "udpFlood": "UDP-Flood", "updateSuccess": "Cybersecurity event updated successfully", "webThreats": "Web threats", "xmasScan": "<PERSON><PERSON>"}}, "management": {"deviceConfiguration": {"configModel": "Configuration Model", "configName": "Configure Name", "createSuccess": "Device configuration created successfully.", "deleteKey": "Device Configuration(s)", "deleteSuccess": "Device configuration(s) deleted successfully", "editConfig": "Edit Configuration", "enterConfigInfo": "Enter Configuration File Information", "firmwareVersion": "Firmware Version", "group": "Group", "isReferenced": "One or more of the selected configuration(s) are referenced.", "lastModifiedTime": "Last Modified Time", "location": "Location", "mac": "MAC Address", "maxTableSize": "The maximum configurations is {{num}}.", "noModelMsg": "There is no model for configuration", "offlineWarning": "Device offline", "onlyAcceptIni": "Only configuration files in '.ini' format are accepted.", "onlyOneFilePerTime": "Only one file can be uploaded at a time.", "selectConfigFile": "Select Configuration File", "selectWarning": "Only allows one device for configuration backup", "serialNumber": "Serial Number", "updateSuccess": "Device settings updated successfully", "uploadConfigFile": "Upload Configuration File (.ini)", "uploadConfigMethod": "Upload Configuration Method", "uploadConfigTitle": "Upload Device Configuration File", "uploadDeviceConfig": "Upload Configuration from Device", "uploadLocalConfig": "Upload Configuration from Local"}, "deviceGroup": {"accessPermission": "Access Permission", "addDevices": "Add Devices", "adminPermission": "Admin users have permission to all groups ", "createGroup": "Create Group", "createSuccess": "Device group created successfully.", "deleteKey": "Delete Device Group(s)", "deleteSuccess": "Device group(s) deleted successfully.", "description": "Description", "deviceCount": "<PERSON><PERSON>", "editGroup": "Edit Device Group", "enterGroupInfo": "Enter Group Information", "firmwareVersion": "Firmware Version", "grantAccessPermission": "Grant Access Permission", "group": "Group", "groupName": "Group Name", "location": "Location", "mac": "MAC", "role": "Role", "serialNumber": "Serial Number", "showAllDevices": "Show All Devices", "showSelectedDevices": "Show Selected Devices", "status": "Status", "updateSuccess": "Device group updated successfully.", "username": "Username"}, "firmware": {"buildTime": "Build Time", "deleteKey": "Firmware", "deleteSuccess": "Firmware deleted successfully.", "description": "Description", "dropZoneTitle": "Upload a firmware file (.rom)", "isReferenced": "One or more of the selected firmware are referenced.", "maxRowMsg": "The maximum number of firmware files is {{ max }}.", "maxSize": "The maximum allowed file size is 1 GB.", "modelSeries": "Model Series", "onlyAcceptRom": "Only firmware files in '.rom' format are accepted.", "onlyOneFilePerTime": "Only one file can be uploaded at a time.", "uploadFirmware": "Upload Firmware", "uploadSuccess": "Firmware uploaded successfully.", "version": "Version"}, "inUse": "Yes", "object": {"filter": {"address": "IP Address and Subnet", "code": "Code", "createObject": "Create Object", "createSuccess": "Object created successfully", "customIpProtocol": "Custom IP Protocol", "decimal": "(Decimal)", "deleteKey": "Object(s)", "deleteSuccess": "Object(s) deleted successfully.", "detail": "Details", "editObject": "Edit Object", "endPort": "Port: End", "icmp": "ICMP", "icmpCode": "ICMP Code", "icmpType": "ICMP Type", "industrialAppService": "Industrial Application Service", "ipAddress": "IP Address", "ipEnd": "IP Address: End", "ipProtocol": "IP Protocol", "ipRange": "IP Range", "ipStart": "IP Address: Start", "ipType": "IP Type", "isReferenced": "One or more of the selected objects are referenced", "leaveAsAny": "Leave blank to represent Any", "maxRowMsg": "The maximum number of objects is {{ max }}.", "name": "Filter", "needSelectedMsg": "Select at least one item", "networkName": "Network Name", "networkService": "Network Service", "objectName": "Name", "objectReference": "Object References", "objectReferenceMsg": "This object is referenced by a policy index in the following profile(s):", "objectType": "Object Type", "port": "Port", "portRange": "TCP and UDP Port Range", "selectIndustrialAppService": "Select Industrial Application Service(s) *", "selectNetworkService": "Select Network Service(s) *", "servicePortType": "Service Port Type", "singleIp": "Single IP", "singlePort": "TCP and UDP Port", "startPort": "Port: Start", "subnet": "Subnet", "subnetMask": "Subnet Mask", "tcp": "TCP", "tcpUdp": "TCP and UDP", "type": "Type", "udp": "UDP", "updateSuccess": "Object updated successfully", "userDefinedService": "User-defined Service"}, "interface": {"bridge": "Bridge", "createInterface": "Create Interface Object", "createSuccess": "Interface created successfully.", "deleteKey": "Interface(s)", "deleteSuccess": "Interface(s) deleted successfully.", "editInterface": "Edit Interface Object", "interfaceName": "Interface Name", "interfaceReference": "Interface References", "interfaceReferenceMsg": "This interface is referenced by a policy index in the following profile(s):", "invalidKey": "The following names are reserved: Any", "isReferenced": "One or more of the selected interfaces are referenced.", "maxRowMsg": "The maximum number of interfaces is {{ max }}.", "mode": "Mode", "name": "Interface", "port": "Port-based", "updateSuccess": "Interface updated successfully.", "vlan": "VLAN", "vlanIdBridgeType": "VLAN ID / Bridge Mode", "zone": "Zone-based"}}, "policyProfile": {"createProfile": "Create Policy Profile", "createSuccess": "Profile created successfully.", "deleteKey": "Profile(s)", "deleteSuccess": "Profile(s) deleted successfully.", "deployment": {"profile_title": "Policy Profile Deployment", "security_title": "Security Package Deployment", "title": "Policy Profile Deployment"}, "dos": {"all_protection_types": "Enable all protection types", "dosLogSetting": "DoS Log Settings", "dosSetting": "DoS Settings", "floodProtection": "Flood Protection", "limit": "Limit", "portScanProtection": "Port-scan Protection", "sessionSYNProtection": "Session SYN Protection", "stat1": "<PERSON><PERSON>", "stat10": "SYN-Flood", "stat11": "ARP-Flood", "stat12": "UDP-Flood", "stat2": "<PERSON><PERSON>", "stat3": "NMAP-<PERSON><PERSON>", "stat4": "SYN/FIN Scan", "stat5": "FIN Scan", "stat6": "NMAP-ID Scan", "stat7": "SYN/RST Scan", "stat8": "TCP Sessions Without SYN", "stat8Tooltip": "Limitation: For asymmetric network architectures and when NAT is enabled, it is strongly advised not to enable \"TCP Sessions Without SYN\" disabled to avoid unexpected disconnections.", "stat9": "ICMP-Flood", "title": "DoS"}, "editProfile": "Edit Policy Profile", "ips": {"accept": "Accept", "category": "Category", "custom": "(custom)", "id": "ID", "impact": "Impact", "monitor": "Monitor", "noPackageMsg": "Check the security packages on the Security Package Management page.", "noVersionAvailable": "No version available", "packageVersion": "Package Version", "reference": "Reference", "reset": "Reset", "ruleSetting": "Rule Settings", "title": "IPS", "updateSuccess": "Rule(s) updated successfully.", "warningMsg": "Before configuring any policies, please make sure the Intrusion Prevention System (IPS) function is enabled on the Firewall > Advanced Protection > Configuration screen in the device's web interface."}, "isReferenced": "One or more of the selected profiles are referenced.", "layer3to7": {"allowAll": "Allow All", "createPolicy": "Create Layer 3-7 Policy", "createSuccess": "Layer 3-7 policy created successfully.", "default_action_log": "Event Log", "default_action_log_destination": "Log Destination", "default_action_severity": "Severity", "defaultAction": "Action", "deleteKey": "Layer 3-7 Policy(s)", "deleteSuccess": "Layer 3-7 policy(s) deleted successfully.", "deleteTitle": "Delete Layer 3-7 Policy", "denyAll": "<PERSON><PERSON>", "destinationAddress": "Destination Address", "destinationPort": "Destination Port or Protocol", "destIpAddress": "Destination IP Address", "destService": "Destination Service", "editPolicy": "<PERSON> Layer 3-7 <PERSON>", "enforce": "Status", "enforcement": "Status", "event": "Event", "eventSetting": "Default Policy Settings", "filterMode": "Filter Mode", "globalSetting": "Global Firewall Settings", "incomingInterface": "Incoming Interface", "ipAndPortFiltering": "IP and Port Filtering", "ipAndSourceMacBinding": "IP and Source MAC Binding", "ipTypeError": "Source Port IP Protocol ({{ source }}) is different from Destination Port IP Protocol ({{ dest }})", "maxRowMsg": "The maximum number of objects is {{ max }}.", "outgoingInterface": "Outgoing Interface", "policyName": "Name", "protocolService": "Protocol and Service", "sourceAddress": "Source Address", "sourceIpAddress": "Source IP Address", "sourceMacAddress": "Source MAC Address", "sourceMacFiltering": "Source MAC Filtering", "sourcePort": "Source Port", "title": "Layer 3-7", "updateSuccess": "Layer 3-7 policy updated successfully."}, "maxRowMsg": "The maximum number of profiles is {{ max }}.", "profileName": "Profile Name", "profileReference": "Profile References", "profileReferenceMsg": "This profile is referenced by the following device(s):", "sessionControl": {"concurrentTcp": "Concurrent TCP Connections", "connectionsRequestUnit": "connections/s", "connectionsUnit": "connections", "createPolicy": "Create Session Control Policy", "createSuccess": "Session control policy created successfully.", "deleteKey": "Session Control Policy(s)", "deleteSuccess": "Session control policy(s) deleted successfully.", "destinationIp": "Destination IP", "destinationPort": "Destination Port", "destIpAddress": "IP Address", "destPort": "Port", "drop": "Drop", "editPolicy": "Edit Session Control Policy", "enforcement": "Status", "maxRowMsg": "The maximum number of policy for this device is {{ max }}.", "monitor": "Monitor", "sub_title": "Network Host and Service Resource Protector", "tcpConnectionLimit": "TCP Connection Limit", "tcpDestError": "IP Address and Port cannot both be Any at the same time", "tcpDestination": "TCP Destination", "tcpLimitError": "You must configure at least one limitation", "tcpLimitMsg": "At least one limitation is required", "title": "Session Control", "totalTcp": "Total TCP Connections", "updateSuccess": "Session control policy updated successfully."}, "tabInspection": "Inspection Objects", "tabInterface": "Interface Objects", "tabPolicyProfile": "Policy Profiles", "title": "Policy Profile Management", "updateSuccess": "Profile updated successfully."}, "scheduleInUse": "Schedule In Use", "scheduling": "Scheduling", "softwarePackage": {"applicationProducts": "Applicable Products", "auto-download": "Auto-download", "bugsFixed": "Bugs Fixed", "buildTime": "Build Time", "changes": "Changes", "checkConnection": "Check the connection status to the Moxa update server.", "checkNewPackage": "Checks for new package versions on MOXA server.\n", "checkSoftwarePackage": "Check for Package Updates", "daily": "Daily", "deleteKey": "Security Package(s)", "deleteSuccess": "Security package(s) deleted successfully.", "description": "Description", "detailInfo": "Detailed Info", "dropZoneTitle": "Upload a package file (.pkg)", "endDate": "End Date", "endTime": "End Time", "enhancements": "Enhancements", "event": "Event", "isReferenced": "One or more of the selected packages are referenced.", "janus": "Network Security Packages", "lastConnectionCheck": "Last Connection Check", "lastSoftwarePackageUpdateResult": "Last Security Package Update Result", "licenseActivationReminder": "License Activation Reminder", "licenseActivationReminderContent": "To ensure enhanced security mechanisms, please activate the license to enable this feature.", "licenseTransferReminder": "License Transfer Reminder", "licenseTransferReminderContent": "To ensure enhanced security mechanisms, please transfer your MXsecurity license before uploading the network security packages.", "local": "Local", "log": "Event Logs", "maxPackageMsg": "Maximum simultaneous downloads: {{ max }} files.", "maxRowMsg": "The maximum number of software packages is {{ max }}.", "maxSize": "The maximum allowed file size is 1 GB.", "message": "Message", "newFeatures": "New Features", "notes": "Notes", "onlyAcceptPkg": "Only files in '.pkg' format are accepted.", "onlyOneFilePerTime": "Only one file can be uploaded at a time.", "packageDownloading": "Package downloader is working for others", "packageReference": "Package References", "packageReferenceMsg": "This package is referenced by the following profile(s):", "period": "Period", "productModel": "Product Model", "releaseDate": "Release Date", "releaseNote": "Release Notes", "scheduling": "Scheduled Update Check", "schedulingMode": "Scheduling Mode", "server": "Moxa Update Server Status", "serverDisconnected": "The security package can only be checked when the server is connected.", "severity": "Severity", "softwarePackageAlreadyLatest": "Security package is up to date.", "softwarePackageCheck": "Security Package Check", "softwarePackagesFile": "Security Packages File", "softwarePackagesUpdateCheck": "Update Security Package", "startDate": "Start Date", "startTime": "Start Time", "supportedFunctions": "Supported Functions", "supportedOperatingSystems": "Supported Operating Systems", "supportModel": "Supported Models", "supportSeries": "Supported Series", "syncSettingNotSet": "Complete the Sync Settings to check the security package.", "syncSettings": "Scheduled Update Check", "syncSettingUpdateSuccess": "Settings updated successfully.", "syncSoftwarePackageBySchedule": "Automatically check for security package updates for the specified models based on a user-specified schedule.", "syncSoftwarePackageByScheduleTooltip": "Configure the frequency at which to check the Moxa server for security package updates.", "time": "Time", "title": "Security Package Management", "updateCheckTooltip": "Check the Moxa server for security package updates to make sure you are using the latest version.", "uploadBy": "Uploaded by", "uploadSoftwarePackage": "Upload Package", "uploadSuccess": "Security package uploaded successfully.", "username": "Username", "version": "Version", "weekday": "Day", "weekly": "Weekly", "zeus": "MXsecurity Agent Packages"}}}, "PORT_SETTING": {"another_port_setting_error": "Another setting is being processed", "apply_another_port": "Apply settings to another port", "disable_port_warning": "Warning: Disabling this port will disconnect the devices connected to this port.", "enable": "Enable", "get_port_setting_fail": "Failed to get port settings", "hint": "*If the port settings fail, please go to device's web console by following this path: Port > Port Settings to confirm whether the selected port can be configured or not.", "media_type": "Media Type", "port": "Port", "port_description": "Port Description", "port_name": "Port Name", "port_select": "You selected", "set_fail": "Failed to update some ports, please try again later", "set_success": "Port settings updated successfully", "title": "Ethernet/Fiber Port Settings"}, "PREFERENCES": {"advanced": "Advanced", "appearance": "Appearance", "default_view": {"choose_start_page": "Choose a start page", "dashboard": "Dashboard", "title": "Default View", "topology": "Topology"}, "device_appearance": {"alias": "<PERSON><PERSON>", "bottom_hint": "If you change the Alias setting, please delete the device on the topology and then rescan or add a device to complete the 'Alias' setting.", "bottom_label": "Bottom Label", "bottom_label_items": {"alias": "<PERSON><PERSON>", "location": "Location", "mac": "MAC", "model_name": "Model Name", "none": "None", "sysname": "SysName"}, "get_fail": "Failed to get device appearance", "ip_address": "IP Address", "set_fail": "Failed to update device appearance", "set_success": "Device appearance updated successfully.", "title": "<PERSON><PERSON>"}, "device": {"login": "Log In", "title": "<PERSON><PERSON>"}, "dialog": {"desc": "Clear all 'Don’t show this message again' settings and show all hidden dialogs again", "title": "Dialog"}, "display": "Appearance", "email_config": {"allow_selfsigned_cert": "<PERSON>ow Self-signed Certificate", "apply_fail": "Failed to update email server configuration", "apply_success": "Email server settings updated successfully", "encryption": "Encryption", "password": "Password", "port_number": "Port Number", "sender_address": "Sender Address", "server_domain_name": "Server Domain Name/IP", "title": "Email Server Configuration", "username": "Username"}, "events": {"apply_fail": "Failed to set event threshold", "apply_success": "Event threshold updated successfully.", "availability_under": "Availability Under", "bandwidth_utilization_over": "Bandwidth Utilization Over", "bandwidth_utilization_under": "Bandwidth Utilization Under", "link_down": "Link Down", "link_up": "Link Up", "packet_error_rate_over": "Packet Error Rate Over", "port_looping": "Port Looping", "sfp_rx_below_threshold": "SFP RX Under", "sfp_temp_over_threshold": "SFP Temperature Over", "sfp_tx_below_threshold": "SFP TX Under", "sfp_volt_below_threshold": "SFP Voltage Under", "sfp_volt_over_threshold": "SFP Voltage Over", "title": "Events"}, "labs": {"colored_link_desc": "When enabled, all wireless links will be colored according to their signal strength.", "desc": "Toggling on Labs will add some experimental features as listed below. These are in early stages, but will not harm your system.", "dialog_title": "Enable Labs Features", "title": "MXview One Labs"}, "language": {"default_language": "Language", "en_US": "English", "fail": "Failed to update language", "success": "Language updated", "title": "Language", "zh_CN": "简体中文", "zh_TW": "繁體中文"}, "login_authentication": {"apply_fail": "Failed to update login authentication", "apply_success": "Login authentication settings updated successfully.", "authentication_protocol": "Authentication Protocol", "local": "Local", "tacacs": "TACACS+", "tacacs_local": "TACACS+, Local", "title": "Login Authentication"}, "login_notification": {"fail": "Failed to update login notification", "login_authentication_failure_message": "Login Authentication Failure Message", "login_message": "Login Message", "show_default_password_notification": "Show Default Password Notification", "show_login_failure_records": "Show Login Failure Records", "success": "Login notification updated", "title": "Login Notification"}, "management_interface": {"help": "This page is used for configuring the connection interfaces from MXview One to devices, including http, https, and telnet ports", "http_port": "HTTP Port", "htts_port": "HTTPS Port", "invalid_port": "Please enter an integer from 1-65535", "set_fail": "Failed to update management interface", "set_success": "Management interface updated successfully.", "telnet_port": "Telnet Port", "title": "Management Interface", "web_console_protocol": "Web Console Protocol"}, "modbus_tcp_configuration": {"port": "port"}, "opc_server_config": {"apply_fail": "Failed to update OPC server configuration", "apply_success": "OPC server settings updated successfully.", "enable_opc_server": "Enable", "title": "OPC Server Configuration"}, "password_policy": {"fail": "Failed to update password policy", "has_digits": "At least one digit (0-9)", "has_special_chars": "At least one special character (~!@#$%^&*-_|;:,.<>[]{}())", "min_password_length": "Minimum length (4 - 16)", "min_password_length_error": "Please enter a valid value", "mixed_case": "Mixed upper and lower case letters (A-Z, a-z)", "password_strength_check": "Password complexity strength check", "success": "Password policy updated", "title": "Password Policy"}, "search": "Search", "security_view": {"all": "All", "awk_device_credentials_hint": "To support security view, you must set the username and password for this device", "basic": "Basic", "basic_text": "Basic", "built_in_profile": "Built-in Profile", "check_item": "Check <PERSON>em", "colors_for_check_result": "Colors for check result", "current_setting": "Current Settings:", "custom": "Custom", "custom_profile": "Select a baseline for the custom profile", "device_security_level": "Device Security Level:", "failed": "Failed to update security view", "filter_result": "results for", "high": "High", "high_text": "High", "medium": "Medium", "medium_text": "Medium", "new_profile": "New profile", "not_pass": "Not Pass", "open": "Open", "pass": "Pass", "profile": "Profile", "profile_details": "Profile details", "success": "Security view updated", "title": "Security View", "unknown": "Unknown", "user_defined": "User defined"}, "Server": "Server", "site_name_configuration": "Site Name Configuration", "sms_config": {"apply_fail": "Failed to update SMS configuration", "apply_success": "SMS settings updated successfully.", "baud_rate": "Baud <PERSON>", "com_port": "COM Port", "mode": "Mode", "title": "SMS Settings"}, "snmp_configuration": {"help": "Set SNMP configuration to access network devices", "title": "SNMP Configuration"}, "SNMP_TRAP": {"apply_fail": "Failed to update SNMP Trap server configuration", "apply_success": "SNMP Trap server settings updated successfully", "community1": "Community of Trap Server 1", "community2": "Community of Trap Server 2", "device_list": "Device List", "device_trap": "SNMP Trap Server of Device", "forward_trap_control1": "Forward Received Trap to Server 1", "forward_trap_control2": "Forward Received Trap to Server 2", "ip1": "IP Address of Trap Server 1", "ip2": "IP Address of Trap Server 2", "mxview_trap": "SNMP Trap Server of MXview One", "version": "SNMP Version", "version_1": "SNMP Version 1", "version_2": "SNMP Version 2c"}, "syslog_config": {"already_running": "Server is already run or stop", "apply_fail": "Failed to update syslog server configuration", "apply_success": "Syslog server settings updated successfully", "enable_syslog_server": "Enable built-in syslog server", "invalid_port": "Please enter an integer from 1-65535", "syslog_server_port": "Syslog server port", "title": "Syslog Server Configuration"}, "system_configuration": {"apply_fail": "Failed to update system configuration", "apply_success": "System settings updated successfully", "background_discovery": "Background Discovery", "disk_hint": "Alarm is disabled if threshold is set to 0", "playback": "Playback", "playback_hint_1": "* When the \"Playback\" function is enabled, MXview One will record the status of devices and links when an event occurs, and you can enter playback mode later to watch the detailed process", "playback_hint_2": "* Additional disk space is required when the \"Playback\" function is enabled", "threshold_disk_space": "Threshold of Disk Space (MB)", "title": "System Configuration"}, "table": {"default": "<PERSON><PERSON><PERSON>", "dense": "<PERSON><PERSON>", "fail": "Failed to update table settings", "success": "Table settings updated", "table_row_height": "Row Height", "title": "Table"}, "tacacs": {"apply_fail": "Failed to update TACACS+ server configuration", "apply_success": "TACACS+ server settings updated successfully", "auth_type": "Auth Type", "auth_type_asc_two": "ASCII", "auth_type_chap": "CHAP", "auth_type_ms_chap": "MS-CHAP", "auth_type_pap": "PAP", "server_address": "Server Address", "share_key": "Share Key", "tcp_port": "TCP Port", "timeout": "Timeout", "title": "TACACS+ Server"}, "title": "Preferences", "topology_appearance": {"access_port": "Access Port", "background": "Background", "background_color": "Background Color", "directed_line_style": "Directed Line Style", "edit_igmp_visualization_color": "Edit IGMP Visualization Color", "edit_traffic_load_color": "Edit Traffic Load Color", "edit_vlan_visualization_color": "Edit VLAN Visualization Color", "elbow_line_style": "Elbow Line Style", "fail": "Failed to update topology appearance", "hsr_ring": "HSR Ring", "igmp_visualization": "IGMP Visualization", "link_down": "Link Down", "link_up": "Link Up", "member": "Member", "poe": "PoE", "poe_color": "PoE Link Color", "prp_lan_a": "PRP LAN A", "prp_lan_b": "PRP LAN B", "querier": "<PERSON><PERSON>", "rstp": "RSTP", "show_poe": "Show PoE Status on Topology", "status_color": "Status Color", "success": "Topology appearance updated", "text_size": "Text Size", "text_size_large": "Large", "text_size_medium": "Medium", "text_size_small": "Small", "title": "Topology", "topology_style": "Topology Line Style", "traffic_load": "Traffic Load", "trunk_port": "Trunk Port", "turbo_chain": "Turbo Chain", "turbo_ring_v1": "Turbo Ring V1", "turbo_ring_v2": "Turbo Ring V2", "vlan_visualization": "VLAN Visualization"}, "user": "User"}, "rogue_device_detection": {"add_device_for_rogue_device_detection": "Add this device to the Device Baseline for Rogue Device Detection", "add_device_to_baseline": "Add Device to Baseline", "add_device_to_baseline_content": "Are you sure you want to add this device to the baseline?", "add_devices_to_baseline_content": "Are you sure you want to add these devices to the baseline?", "add_scan_device_for_rogue_device_detection": "Add the scanned devices to the Device Baseline for Rogue Device Detection", "clear_all_rogue_device_history": "Clear Rogue Device History", "clear_all_rogue_device_history_hint": "All rogue device history will be cleared. Are you sure you want to continue?", "connected_switch_port": "Connected Switch/Port", "creation_time": "Created on", "current_rogue_device": "Current Rogue Devices", "delete_device_from_baseline": "Delete Device From Baseline", "delete_device_from_baseline_content": "This device will be removed from the baseline and added as a rogue device.", "delete_devices_from_baseline_content": "These devices will be removed from the baseline and added as rogue devices.", "device_baseline": "<PERSON><PERSON>", "device_baseline_content": "This operation will create a new baseline and will overwrite the existing one.", "download_all_history_data_to_csv": "Export All History Data to CSV", "download_current_page_to_csv": "Export Current Page to CSV", "first_seen": "First Seen", "ip": "IP Address", "last_seen": "Last Seen", "mac": "MAC Address", "must_create_a_baseline_first": "No device baseline exist. Create a baseline first.", "no_devices_can_add": "No devices detected. Add devices to MXview One first. ", "port": "Port", "rogue_device_history": "Rogue Device History", "rogue_device_settings": "<PERSON> Device <PERSON>s", "sequence_no": "Sequence No.", "unknown": "Unknown", "vendor": "NIC Vendor"}, "SCAN_RANGE": {"add_scan_range": "Add Scan Range", "button": {"back": "Back", "browse_topology": "Browse Topology", "cancel": "Cancel", "discovery": "OK and Run Discovery", "next": "Next", "recover": "Recovery", "scan_new_network": "Scan New Network"}, "cidr_address_range": "CIDR Address Range", "duplicate_range": "New range overlaps with existing ranges", "edit_scan_range": "<PERSON>", "firstIp_higher_lastIp": "IP Range is invalid (First IP > Last IP)", "subnet_mask": "Subnet Mask", "table_title": {"active": "Enable Scan Range", "background_scan": "Background Scanning", "conflict_scan": "IP Conflict Detection", "edit": "Edit", "end_ip": "Last IP Address", "group": "Group", "name": "Name", "site_name": "Site Name", "start_ip": "First IP Address"}, "wizard": {"complete": "Complete", "complete_message": "{{discoveryDevices}} device(s) added to MXview One", "discovery_result": "Discovery Result", "network_range": "Network Range(s)", "save_hint": "Scanned range(s) will be saved after device discovery.", "title": "<PERSON>ce Discovery"}}, "script_automation": {"add_a_script_automation": "Add a Script Automation", "add_script_button_hint": "The maximum number of script automations is 200.", "add_script_first_hint": "No script automations found. Go to the Script Automation screen to add a script automation.", "add_script_sutomation": "Add Script Automation", "adjustable_buttons": "Reorder <PERSON>", "affected_devices": "Affected Devices", "affected_devices_info": "This action will affect {{ affectedDevices }} devices.", "affected_devices_info_2": "This action will affect the following {{ affectedDevices }} devices", "align_buttons": "Align all groups in a single column", "all_devices": "All Devices", "automation_button": "Automation Buttons", "background_color": "Background Color", "button_name": "Button Name", "button": {"panel": "Panel", "privilege": "Require Admin <PERSON>", "state": {"hint": "Only one button in a group can be in the 'On' state at any given time.", "off": "Off", "on": "On", "title": "State"}, "style": "Display Mode", "widget": "Widget"}, "cli_id_duplicated": "This CLI has already been selected.", "cli_script_and_target_device": "CLI Scripts and Target Devices", "cli_script_hint": "The maximum number of scripts is 50.", "color": "Color", "confirm_proceed": "Do you want to proceed?", "create_new_group": "Create and add to a new group", "delete_automation": "Are you sure you want to delete this script automation?", "delete_multiple_automation": "Are you sure you want to delete these script automations?", "delete_script_automation": "Delete Script Automation", "description": "Description", "device_missing": "The following devices could not be found", "drag_button": "Drag a button here to create a new group.", "edit_button": "<PERSON>", "edit_group": "Edit Group", "edit_panel": "Edit Button Panel Name", "edit_script_button": "Edit Script Automation", "execute_button": "Execute <PERSON>", "execute_button_hint": "Executing Script Automation", "execute_button_info": "Please wait for the process to finish to view the results. If you leave this screen, you can go to Saved CLI Scripts > Execution Results to download the results.", "extra_large": "Extra Large", "group": "Group", "group_already_exist": "This group name already exists", "group_name": "Group Name", "invalid_account": "Invalid account privilege", "ip_duplicated": "This device is already selected.", "large": "Large", "last_executed": "Last Executed", "leave_page_hint": "Are you sure you want to leave this page?", "leave_without_saving": "Leave Without Saving", "medium": "Medium", "more": "more", "name": "Name", "not_change_group": "Use the current group", "not_saved_hint": "Any changes you made will not be saved.", "script_automation": "Script Automation", "select_all": "Select All", "select_existing_group": "Move to another group", "select_saved_cli_script": "Select Saved CLI Script", "small": "Small", "start_preview": "Preview in Topology", "stop_preview": "Stop Preview", "target_device": "Target Devices", "text_color": "Text Color", "widget_size": "Widget Size"}, "SECURITY_ITEM": {"ACCESSIBLE_IP_LIST": "Enable Trusted Access", "ACCOUNT_LOCKOUT": "Enable Account Login Failure Lockout", "ACCOUNT_VALIDITY": "Account and Password Policy Validity", "AUTO_LOGOUT": "Enable Auto Logout", "AWK_SERIES": "Wireless", "BROAD_CAST_STORM": "Enable DDoS Protection", "changed": "Changed", "disabled": "Disabled", "enabled": "Enabled", "ENCRYPT_CONSOLE": "Disable Non-encrypted TCP/UDP Ports", "ENCRYPTED_CONFIG": "Enable Configuration File Encryption", "HIGH_SECURE_MODE": "High Secure Mode", "LOGIN_NOTIFICATION": "Set Login Message", "MGATE": "Gateway", "non-changed": "Unchanged", "not_set": "Not set", "NPORT": "Terminal Server", "NPORT5000": "Device Server", "NTP_SERVER": "Set NTP Client", "PASSWORD_CHANGED": "Change Default Password / SNMP Community String", "PASSWORD_POLICY": "Enable Password Complexity Strength Check", "read_fail": "Read fail", "set": "Set", "SWITCH": "Switch", "SYSLOG": "Set Syslog Server", "TRAPSYSLOG": "Set SNMP Trap/Inform or Syslog Server", "unknown": "Unknown", "WEB_CERTIFICATE": "Import Web Certificate"}, "SERIAL_PORT_MONITORING": {"all_ports": "All Ports", "any_serial_error_count": "Any Serial Error Count", "break_error_count": "Break Error Count", "copy_configuration_device": "Copy Configuration to Devi<PERSON>", "count_threshold": "<PERSON><PERSON>", "counts": "Count(s)", "critical": "Critical", "error_status": "Serial Port Warnings", "event_condition_rule": "Event Trigger Rules\n", "frame_error_count": "<PERSON><PERSON>", "hint_any": "Try the steps below to resolve the following issue: Serial port {{portnum}} has exceeded the overrun error ({{overrun error count}}), parity error ({{parity error count}}), frame error ({{frame error count}}), or break error ({{break error count}}) count threshold.", "hint_break": "Try the steps below to resolve the following issue: Serial port {{portnum}} has exceeded the break error count threshold ({{count}}).\nA serial break signal indicates a special condition on the connected serial device, such as a wiring issue, device malfunction, device reset, or synchronization process.", "hint_frame": "Try the steps below to resolve the following issue: Serial port {{portnum}} has exceeded the frame error count threshold ({{count}}).\nWhen the Moxa device receives serial data, it checks if the frame format matches the serial parameters. If they do not match, it will count as a frame error. ", "hint_general_1": "If the suggested solution did not work or you have further questions, please contact your", "hint_general_2": "first.", "hint_general_3": "Contact", "hint_general_4": "if you still need additional support.", "hint_overrun": "Try the steps below to resolve the following issue: Serial port {{portnum}} has exceeded the overrun error count threshold ({{count}}).\nIf the connected serial devices send data too quickly for the Moxa device to read, it will drop data, causing an overrun error.", "hint_parity": "Try the steps below to resolve the following issue: Serial port {{portnum}} has exceeded the parity error count threshold ({{count}}).\nA parity error indicates that the received data character does not match the configured parity.", "hint_rx": "Try the steps below to resolve the following issue: Serial port {{portnum}} has not received any data in the last {{min}} minute(s).", "hint_rxtx": "Try the steps below to resolve the following issue: Serial port {{portnum}} has not transmitted or received any data in the last {{min}} minute(s).", "hint_tx": "Try the steps below to resolve the following issue: Serial port {{portnum}} has not transmitted any data in the last {{min}} minute(s).", "how_to_resolve": "How to resolve?", "minutes": "Minutes", "no_data_period": "No Data Period", "overrun__error_count": "Overrun Error Count", "parity__error_count": "Parity Error Count", "port_duplicated": "This port/trigger rule combination is already configured.", "port_properties": "Event Type", "resolve_title": "Resolve Issue on Serial Port {{portnum}}", "rx": "RX Inactivity", "serial_port": "Serial Port", "severity": "Severity", "step1_break": "Check if the connected serial devices are working properly.", "step1_frame": "Check if the serial interface settings (RS-232, RS-422, RS485) and the communication parameters (e.g. 115200, 8, n, 1) on the Moxa device match those on the connected serial devices.", "step1_ovrrun": "Check if the serial hardware and/or software flow control is properly set on both the Moxa device and the connected serial devices", "step1_parity": "Check if the parity and baudrate settings on the Moxa device and connected serial devices match. ", "step1_txrx": "Check if the serial cable between the Moxa device and the serial end devices is connected properly.", "step2_ovrrun": "Check if the FIFO needs to be enabled on the Moxa device.", "step2_parity": "In areas with high interference, check if the serial communication system is properly protected.", "step2_txrx": "Check if the connected serial devices are working properly.", "step3_parity": "Check the connected serial devices for wiring problems or hardware faults.", "still_not_working": "Still not working?", "title": "Serial Port Events", "tx": "TX Inactivity", "tx_rx": "TX & RX Inactivity", "warning": "Warning"}, "SEVERITY": {"critical": "Critical", "information": "Information", "title": "Severity", "warning": "Warning"}, "sfpList": {"rx": "RX (dBm)", "temperature": "Temp. (°C)", "title": "SFP List", "tx": "TX (dBm)", "voltage": "Volt. (V)"}, "SITE_MANAGEMENT": {"desc": "Description", "fail": "Failed to update site information", "name": "Name", "offline": "Site offline ({{siteName}})", "online": "Site online ({{siteName}})", "success": "Site information updated", "title": "Site Management"}, "SITE_MENU": {"management": "Management"}, "SITE_PROPERTIES": {"description": "Description", "devices": "Devices (Normal / Warning / Critical)", "information": "Information", "name": "Name", "title": "Site Properties"}, "SNACK_BAR": {"acking": "Acking...", "copied": "<PERSON>pied", "deleting": "Deleting...", "saving": "Saving..."}, "SYSLOG_SETTINGS": {"all_severity": "All", "authentication": "Authentication", "enable_syslog_forward": "Syslog Forwarding", "enable_tcp": "Enable (TCP only)", "enable_udp": "Enable (UDP only)", "enable_udp_tcp": "Enable (UDP & TCP)", "failed_to_get_syslog_forward_settings": "Failed to get the syslog forwarding settings", "failed_to_get_syslog_settings": "Failed to get syslog settings", "filter_settings_hint": "You can enter multiple source IP addresses, separated by a comma.", "forward_ip1": "Remote IP/Domain Name 1", "forward_ip2": "Remote IP/Domain Name 2", "port_1": "Port 1", "port_2": "Port 2", "protocol": "Protocol", "source_ip": "Source IP", "syslog_built_in": "Built-in Syslog Server", "syslog_filter_settings": "Syslog Filters ", "syslog_forwarding": "Syslog Forwarding", "syslog_server_settings": "Syslog Server Settings", "tcp": "TCP", "tcp_port": "TCP Port", "title": "Syslog Settings", "tls_cert": "TLS + certificate", "tls_only": "TLS only", "udp": "UDP", "udp_port": "UDP Port", "update_failed": "Failed to update settings", "update_success": "Settings updated successfully"}, "SYSLOG_VIEWER": {"device_ip": "IP Address", "facility": "Facility", "filter_syslog_event": "Type to filter syslog events", "ip_error": "Please enter a valid IP address", "message": "Message", "priority": {"equals": "Equals", "high_than": "Higher than or equal to", "lower_than": "Lower than or equal to", "title": "Priority"}, "severity": {"alert": "<PERSON><PERSON>", "critical": "Critical", "debug": "Debug", "emergency": "Emergency", "error": "Error", "information": "Information", "notice": "Notice", "title": "Severity", "warning": "Warning"}, "site_name": "Site Name", "timestamp": "Time Stamp", "title": "Syslog Viewer"}, "SYSTEM": {"request_timeout_title": "Request Timed Out", "trigger_disconnected_desc1": "Disconnected from MXview One server.", "trigger_disconnected_desc2": "Reconnecting after 5 seconds...", "unauthorized_desc": "Access is denied due to invalid credentials.", "unauthorized_title": "Unauthorized"}, "TABLE": {"add": "Add", "adjustable_columns": "Adjustable <PERSON><PERSON>ns", "compare": "Compare", "delete": "Delete", "edit": "Edit", "edit_columns": "<PERSON>", "enable": "Enabled/Disabled", "export": "Export", "exporting": "Now exporting...", "filter": "Filter", "import": "Import", "limit_count": "<PERSON>. ", "list_collaspe": "Collapse", "list_expand": "Expand for more information", "locate": "Locate", "no_data": "No data to display", "not_support": "The device's firmware version is not supported", "save": "Save", "search": "Search", "selected_count": "Selected", "show_log": "Show log", "sync": "Sync", "total": "Total", "waiting_data": "Waiting for data"}, "TOPOLOGY": {"add_tag": "Add Tag...", "add_tag_fail": "Failed to add tag", "add_tag_success": "Tag added successfully", "choose_goose_publisher": "Choose a GOOSE publisher", "colored_link": "Colored links according to signal strength", "delete_tag_failed": "Failed to delete tag", "delete_tag_success": "Tag deleted successfully", "device_not_found": "Devi<PERSON> not found", "display_opt": "Display options", "dynamic_wireless_client_position": "Dynamic client position", "dynamic_wireless_client_position_desc": "Enable this option to show each client next to the AP it is currently connected to", "editTagTooltip": "Press Enter to save.\nPress ESC key to cancel.", "goose": "GOOSE", "goose_publisher": "GOOSE Publisher", "goose_tampered": "GOOSE Tampered", "goose_timeout": "GOOSE Timeout", "grouping_failed": "Failed to group.", "grouping_success": "Group successfully", "legend": "Legend", "new_tag": "New Tag", "no_subscriber": "No subscribers", "prp_hsr": "PRP/HSR", "prp_hsr_tags": "PRP/HSR Tags", "publisher": "Publisher", "publisher_hint": "GOOSE control block name\nAPPID / Address", "search_topology": "Search topology", "set_tag_fail": "Failed to set tag", "set_tag_success": "Tag set successfully", "show_all_wireless_clients": "Show clients", "site_management_not_supported": "Please specify a site to use the Roaming history playback function", "subscriber": "Subscriber", "subscriber_hint": "IED Name / GOOSE control block name", "tag": "Tag", "traffic_view": "Traffic Load(%)", "ungrouping_failed": "Failed to ungroup.", "ungrouping_success": "Ungroup successfully.", "wireless_display_opt": "Wireless display options", "zoom_in": "Zoom in", "zoom_out": "Zoom out", "zoom_to_actual_size": "Zoom to default size", "zoom_to_fit": "Zoom to fit"}, "TRAP_CONFIGURATION": {"apply_fail": "Failed to update device Trap server settings", "apply_success": "Device Trap server settings updated successfully", "community_name1": "Community Name 1", "community_name2": "Community Name 2", "destination_ip1": "Destination IP 1", "destination_ip2": "Destination IP 2", "title": "Trap Server"}, "UNIT": {"dB": "dB", "dBi": "dBi", "dBm": "dBm", "hours": "hr", "mb": "Mb", "mbps": "Mbps", "meter": "m", "min": "min", "sec": "sec", "times": "times"}, "UPGRADE_FIRMWARE": {"file_type_error": "Firmware files must be in .rom, .tar, and .gz file format.", "upgrade_firmware_fail": "Failed to upgrade firmware", "upgrade_firmware_success": "Firmware upgraded successfully", "upgrading": "Downloading the firmware file; this operation may take several minutes. Do not turn the power off or disconnect from the network until the process is completed"}, "validators": {"duplicateEmail": "There has duplicate emails", "excludeLastPassword": "The new password cannot be the same as the last password", "excludeUserName": "Cannot include the username", "invalid": "Invalid character(s)", "invalid_date": "Invalid Date", "invalid_format_allow_space": "Only a maximum of {{count}} spaces allowed in the string", "invalidEmail": "Invalid email", "invalidIpAddress": "Invalid IP address", "invalidIpAddressOrDomainName": "Invalid IP address or Domain Name", "invalidLocation": "Special allow (-_@!#$%^&*().,/)", "invalidMacAddress": "Invalid MAC address", "invalidMacAddressAllZero": "MAC address 00:00:00:00:00:00 is reserved", "invalidSeverity": "Invalid Severity", "ipRangeError": "IP Address: End needs to be greater than IP Address: Start", "isExist": "already exists", "isExistOrUsedByOtherUser": "already exists or used by other user", "maxReceiverSize": "The MAX. receivers is {{num}}.", "needDigit": "Must include at least one digit (0 - 9)", "needGreaterThan": "{{ largeItem }} needs to be greater than {{ smallItem }}", "needLowerCase": "Must include at least one lowercase character (a - z)", "needSpecialCharacter": "Must include at least one special character (~!@#$%^&*_-+=`|\\(){}[]:;”’<>,.?/)", "needUpperCase": "Must include at least one uppercase character (A - Z)", "notMeetPolicy": "Does not meet the password policy requirements", "portRangeError": "Port: End needs to be greater than Port: Start", "pwdNotMatch": "Password does not match", "range": "Invalid range ({{ min }} ~ {{ max }})", "required": "Required", "requireMaxLength": "Must be no more than {{ number }} characters long", "requireMinLength": "Must be at least {{ number }} characters long"}, "Validators": {"require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}"}, "VLAN_TABLE": {"access_ports": "Access Ports", "device_ip": "Device IP", "empty": "Empty", "export_csv": "Export CSV", "filter_vlan": "Type to filter device VLANs", "hybrid_ports": "Hybrid Ports", "location": "Location", "management_vlan": "Management VLAN", "model": "Model", "no": "No", "site_name": "Site Name", "title": "VLAN", "trunk_ports": "Trunk Ports", "vlan_id": "VLAN ID", "yes": "Yes"}, "wirelessPlayback": {"decreaseSpeed": "Decrease Speed", "increaseSpeed": "Increase Speed", "noData": "There is no data in the selected date range", "range": "Duration", "startTime": "Start Time", "timeRange": "Time Range"}}