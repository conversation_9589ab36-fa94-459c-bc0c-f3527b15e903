import { ChangeDetectorRef } from '@angular/core';

// import { trigger } from '@angular/animations';

import * as _ from 'lodash';

import { SEVERITY_LIST } from '../../../pages/network/serial-port-monitoring/serial-port-monitoring.model';
import { SerialPortSeverity } from '../../../pages/network/serial-port-monitoring/serial-port-monitoring.model';
import { Action, BottomLabel, DataType, Severity } from '../../Service/mx-platform/DataDef/DataTypeDefinitions';
import {
  DeviceUnit,
  SerialPortTriggerDevice,
  SerialPortStatusResult,
  SerialPortStatus,
  SerialPortTriggerDeviceResult,
  TriggerMapping,
} from '../../Service/mx-platform/DataDef/DevicesDataDefs';
import { LinkUnit } from '../../Service/mx-platform/DataDef/LinkDataDefs';
import { GoosePublisher, GooseSubscriber, IedDeviceUnit } from '../../Service/mx-platform/DataDef/SiteDataDefs';
import { ServerConfigurationService } from '../../Service/mx-platform/MXviewGatewayConfig';
import { MxTopologyD3 } from './mx-topology-d3';
import { MxTopologyD3Event } from './mx-topology-d3-event';
import { MxTopologyData } from './mx-topology-data';
import { MxTopologyDirectedLink } from './mx-topology-directed-link';
import { MxTopologyElbowLink } from './mx-topology-elbow-link';
import { MxTopologyEvent, MxTopologyEventType } from './mx-topology-event';
import { MxTopologyGoose } from './mx-topology-goose';
import { MxTopologyNeighborDevice } from './mx-topology-neighbor-device';
import { MxTopologyPrpHsr } from './mx-topology-prp-hsr';
import { MxTopologyUtils } from './mx-topology-utils';
import {
  TopologyViewMode,
  circleRadius,
  deviceRedundancy,
  deviceRedundancyText,
  deviceRoleTextOffset,
  deviceRoleTextPosition,
  largeWidthIcons,
  largeWidthIconsExcpetion,
  moxaSysObjId,
  routerRole,
  routerRoleName,
  securityViewDefaultColor,
  securityViewLevelText,
  topologyStyle,
  vlanViewColor,
  wirelessRole,
  wirelessRoleString,
} from './mx-topology.constants';
import { MxTopologyService } from './mx-topology.service';

export class MxTopologyDevice {
  deviceElement: any;
  v3TrapParseStatusSuccess = 0;
  v3TrapParseStatusFail = 1;
  private deviceEnterElement: any;
  private deviceDataElement: any;
  private deviceRoleElement: any;
  private viewMode = 0;
  private gooseClearStatusTimeout;
  private moxaModelName = 'MOXA Device';
  private needsRedraw = false;

  constructor(
    private topologyService: MxTopologyService,
    private mxD3: MxTopologyD3,
    private mxD3Event: MxTopologyD3Event,
    private mxDirectedLink: MxTopologyDirectedLink,
    private mxElbowLink: MxTopologyElbowLink,
    private mxNeighborDevice: MxTopologyNeighborDevice,
    private mxData: MxTopologyData,
    private mxUtils: MxTopologyUtils,
    private mxPrpHsr: MxTopologyPrpHsr,
    private mxGoose: MxTopologyGoose
  ) {}

  updateDeviceElement(
    action: number,
    viewMode: number,
    securityViewFilterName: string,
    updateSecurityFilter: boolean,
    id?: number
  ): void {
    const nodes = this.mxData.nodes.filter(node => node.type === 'device');
    this.deviceDataElement = this.mxD3.d3GForeground.selectAll('g.device').data(nodes, (d: any) => d.id);

    if (action === Action.REMOVE) {
      this.deviceDataElement.exit().remove();
      return;
    }

    this.viewMode = viewMode;
    this.mxData.securityViewFilterName = securityViewFilterName;

    if (action === Action.NEW) {
      this.deviceEnterElement = this.deviceDataElement
        .enter()
        .append('g')
        .attr('class', 'node device')
        .call(
          this.mxD3.d3
            .drag<SVGSVGElement, any>()
            .on('start', obj => this.mxD3Event.dragStarted(obj))
            .on('drag', obj => this.mxD3Event.dragged(obj))
            .on('end', obj => this.mxD3Event.dragEnded(obj))
        )
        .on('click', obj => this.mxD3Event.onClick(obj));
      if (id !== undefined) {
        this.deviceEnterElement = this.deviceEnterElement
          .attr('id', `device_${id}`)
          .attr('transform', (d: DeviceUnit) => (d.x && d.y ? `translate(${d.x},${d.y})` : null));
      }

      this.createDeviceElement();
    }

    if (this.viewMode === TopologyViewMode.security) {
      this.createSecurityDeviceElement();
    } else if (this.viewMode === TopologyViewMode.vlan) {
      this.creatVlanDeviceElement();
    } else {
      this.deviceEnterElement.selectAll('circle.security-view-circle').remove();
      this.deviceEnterElement.selectAll('circle.vlan-view-circle').remove();
    }

    this.deviceElement = this.deviceEnterElement.merge(this.deviceDataElement);

    if (id === undefined) {
      this.updateDeviceStyle(true);
    } else if (action === Action.NEW) {
      this.updateDeviceStyle(true, id);
    }

    if (updateSecurityFilter && this.viewMode === TopologyViewMode.security) {
      this.updateSecurityFilter();
    }
  }

  createDeviceElement() {
    const nodes = this.mxData.nodes.filter(node => node.type === 'device');
    // Create device selection
    this.deviceEnterElement
      .append('circle')
      .attr('class', 'normal')
      .attr('r', circleRadius.node)
      .attr('stroke-width', '4px')
      .attr('cx', 0)
      .attr('cy', 0);
    // Create device text
    this.deviceEnterElement
      .append('text')
      .attr('font-size', this.mxData.topologyTextSize)
      .attr('id', 'device-text')
      .attr('text-anchor', 'middle')
      .attr('x', 0)
      .attr('y', 35);
    if (this.viewMode === TopologyViewMode.wirelessPlanner) {
      const heatmapCircleElement = this.mxD3.heatmapCircle.selectAll('circle').data(nodes, (d: any) => d.id);
      heatmapCircleElement
        .enter()
        .append('circle')
        .attr('id', d => `distance-circle-${d.id})`)
        .attr('class', 'distance-circle')
        .attr('cx', 0)
        .attr('cy', 0)
        .attr('r', 0)
        .style('fill-opacity', '0.8')
        .style('fill', d => `url(#radial-gradient-${d.id})`)
        .attr('transform', d => `translate(${d.x},${d.y})`);
    }

    if (this.mxData.deviceAppearanceData.bottomLabelType !== BottomLabel.NONE) {
      this.deviceEnterElement
        .append('text')
        .attr('font-size', this.mxData.topologyTextSize)
        .attr('id', 'device-label-text')
        .attr('text-anchor', 'middle')
        .attr('x', 0)
        .attr('y', 50);
    }

    // Create device image
    this.deviceEnterElement.append('image').attr('width', 50).attr('height', 50).attr('x', -25).attr('y', -30);
  }

  createSecurityDeviceElement() {
    this.deviceEnterElement.selectAll('circle').remove();
    this.deviceEnterElement.selectAll('text').remove();
    this.deviceEnterElement
      .append('circle')
      .attr('class', 'security-view-circle')
      .attr('r', circleRadius.site)
      .attr('stroke-width', '4px')
      .attr('cx', 0)
      .attr('cy', 0);
    this.deviceEnterElement
      .append('circle')
      .attr('class', 'normal')
      .attr('r', circleRadius.node)
      .attr('stroke-width', '4px')
      .attr('cx', 0)
      .attr('cy', 0);
    // Create device text
    this.deviceEnterElement
      .append('text')
      .attr('font-size', this.mxData.topologyTextSize)
      .attr('id', 'device-text')
      .attr('text-anchor', 'middle')
      .attr('x', 0)
      .attr('y', 35);
    if (this.mxData.deviceAppearanceData.bottomLabelType !== BottomLabel.NONE) {
      this.deviceEnterElement
        .append('text')
        .attr('font-size', this.mxData.topologyTextSize)
        .attr('id', 'device-label-text')
        .attr('text-anchor', 'middle')
        .attr('x', 0)
        .attr('y', 50);
    }
  }

  creatVlanDeviceElement() {
    this.deviceEnterElement.selectAll('circle').remove();
    this.deviceEnterElement.selectAll('text').remove();
    this.deviceEnterElement.selectAll('circle').remove();
    this.deviceEnterElement.selectAll('text').remove();
    this.deviceEnterElement
      .append('circle')
      .attr('class', 'vlan-view-circle')
      .attr('r', circleRadius.site)
      .attr('stroke-width', '4px')
      .attr('cx', 0)
      .attr('cy', 0);
    this.deviceEnterElement
      .append('circle')
      .attr('class', 'normal')
      .attr('r', circleRadius.node)
      .attr('stroke-width', '4px')
      .attr('cx', 0)
      .attr('cy', 0);
    // Create device text
    this.deviceEnterElement
      .append('text')
      .attr('font-size', this.mxData.topologyTextSize)
      .attr('id', 'device-text')
      .attr('text-anchor', 'middle')
      .attr('x', 0)
      .attr('y', 35);
    if (this.mxData.deviceAppearanceData.bottomLabelType !== BottomLabel.NONE) {
      this.deviceEnterElement
        .append('text')
        .attr('font-size', this.mxData.topologyTextSize)
        .attr('id', 'device-label-text')
        .attr('text-anchor', 'middle')
        .attr('x', 0)
        .attr('y', 50);
    }
  }

  updateSecurityFilter() {
    const nodes = this.mxData.nodes.filter(node => node.type === 'device');
    this.mxData.securityViewDevices = [];

    const filterActions = {
      [securityViewLevelText.all]: () => this.setFilterAllDevices(nodes),
      [securityViewLevelText.high]: () => this.updateSecurityViewDevices(nodes, securityViewLevelText.high),
      [securityViewLevelText.medium]: () => this.updateSecurityViewDevices(nodes, securityViewLevelText.medium),
      [securityViewLevelText.basic]: () => this.updateSecurityViewDevices(nodes, securityViewLevelText.basic),
      [securityViewLevelText.open]: () => this.updateSecurityViewDevices(nodes, securityViewLevelText.open),
      [securityViewLevelText.pass]: () => this.updateSecurityViewDevices(nodes, securityViewLevelText.pass),
      [securityViewLevelText.notPass]: () => this.updateSecurityViewDevices(nodes, securityViewLevelText.notPass),
      [securityViewLevelText.unknown]: () => this.updateSecurityViewDevices(nodes, securityViewLevelText.unknown),
    };

    const action = filterActions[this.mxData.securityViewFilterName];
    if (action) {
      action();
    }

    this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.UPDATE_SECURITY_VIEW_DEVICES, null));
  }

  setFilterAllDevices(nodes: any[]): void {
    this.mxData.securityViewDevices = nodes;
    this.mxData.securityViewDevices = this.mxUtils.sortIp(this.mxData.securityViewDevices);
  }

  updateSecurityViewDevices(nodes, level: string): void {
    const securityViewData = this.mxData.securityViewData[0].result;
    const nodeMap = new Map(nodes.map(node => [node.ip, node]));

    this.mxData.securityViewDevices = securityViewData
      .filter(item => {
        item.security_level = item.security_level || securityViewLevelText.unknown;
        return item.security_level === level;
      })
      .map(item => nodeMap.get(item.ip))
      .filter(Boolean);

    this.mxData.securityViewDevices = this.mxUtils.sortIp(this.mxData.securityViewDevices);
  }

  updateDeviceStyle(updateStyle: boolean, id?): void {
    const selection =
      id !== undefined ? this.mxD3.d3GForeground.select<SVGGElement>(`g#device_${id}`) : this.deviceElement;

    selection
      .attr('id', d => `device_${d.id}`)
      .attr('transform', d => (d.x && d.y ? `translate(${d.x},${d.y})` : null));

    this.updateDeviceSelection(id);

    if (!updateStyle) return;

    this.updateDeviceText(id);
    this.updateDeviceImage(id);
    this.updateDeviceTrapParseStatusIcon(id);
    this.updateDeviceRole(id);
    this.updateTooltip(id);
    this.mxGoose.updateIedName(id);
  }

  updateDeviceSelection(id?: number): void {
    const viewModeConfig = {
      [TopologyViewMode.security]: {
        selector: 'circle.security-view-circle',
        attribute: 'stroke',
        updateFunction: this.updateSecurityViewSelection.bind(this),
      },
      [TopologyViewMode.vlan]: {
        selector: 'circle.vlan-view-circle',
        attribute: 'stroke',
        updateFunction: this.updateVlanViewSelection.bind(this),
      },
      default: {
        selector: 'circle.normal',
        attribute: 'class',
        updateFunction: this.updateSelection.bind(this),
      },
    };

    const config = viewModeConfig[this.viewMode] || viewModeConfig.default;

    if (id !== undefined) {
      this.mxD3.d3GForeground
        .select<SVGGElement>(`g#device_${id} ${config.selector}`)
        .attr(config.attribute, config.updateFunction);
    } else {
      this.deviceElement.select(config.selector).attr(config.attribute, config.updateFunction);
    }
  }

  updateSecurityViewSelection(d: DeviceUnit): string {
    const { securityViewData, securityViewPreferenceData, securityViewFilterName } = this.mxData;
    const profile = securityViewPreferenceData[0].profile;
    const colors = securityViewPreferenceData[0].color;
    const securityItem = securityViewData[0]?.result?.filter(item => item.ip === d.ip) ?? [];
    const securityLevel = securityItem[0]?.security_level ?? securityViewLevelText.unknown;

    if (this.isSecurityViewInvalidDevice(d, securityViewData)) {
      return securityViewDefaultColor.unknown;
    }

    if (securityViewFilterName !== securityViewLevelText.all) {
      return this.getColorForSecurityViewFiltered(securityViewFilterName, securityLevel, colors);
    } else if (securityViewFilterName === securityViewLevelText.all && profile === 1) {
      return this.getColorForSecurityViewProfileOne(securityLevel, colors);
    } else if (securityViewFilterName === securityViewLevelText.all && profile === 0) {
      return this.getColorForSecurityViewProfileZero(securityLevel, colors);
    }
  }

  isSecurityViewInvalidDevice(d: DeviceUnit, securityViewData: any[]): boolean {
    return (
      !securityViewData[0] ||
      !securityViewData[0] ||
      !d.sysobjid ||
      d.sysobjid.indexOf(moxaSysObjId) === -1 ||
      d.model.indexOf(this.moxaModelName) !== -1
    );
  }

  getColorForSecurityViewFiltered(securityViewFilterName: string, securityLevel: string, colors: any): string {
    const colorMap = {
      [securityViewLevelText.high]: colors.high,
      [securityViewLevelText.medium]: colors.medium,
      [securityViewLevelText.basic]: colors.basic,
      [securityViewLevelText.open]: colors.open,
      [securityViewLevelText.pass]: colors.passed,
      [securityViewLevelText.notPass]: colors.failed,
    };

    if (securityViewFilterName === securityLevel && securityLevel == securityViewLevelText.unknown) {
      return securityViewDefaultColor.unknown;
    }

    return securityViewFilterName === securityLevel && colorMap[securityLevel]
      ? this.mxUtils.removeHexAlpha(colorMap[securityLevel])
      : securityViewDefaultColor.none;
  }

  getColorForSecurityViewProfileOne(securityLevel: string, colors: any): string {
    const colorMap = {
      [securityViewLevelText.high]: colors.high,
      [securityViewLevelText.medium]: colors.medium,
      [securityViewLevelText.basic]: colors.basic,
      [securityViewLevelText.open]: colors.open,
    };
    return colorMap[securityLevel]
      ? this.mxUtils.removeHexAlpha(colorMap[securityLevel])
      : securityViewDefaultColor.unknown;
  }

  getColorForSecurityViewProfileZero(securityLevel: string, colors: any): string {
    const colorMap = {
      [securityViewLevelText.pass]: colors.passed,
      [securityViewLevelText.notPass]: colors.failed,
    };
    return colorMap[securityLevel]
      ? this.mxUtils.removeHexAlpha(colorMap[securityLevel])
      : securityViewDefaultColor.unknown;
  }

  updateVlanViewSelection(d: DeviceUnit): string {
    if (this.mxData.devicesVlanData && this.mxData.devicesVlanData.length > 0) {
      const vlanViewData = this.mxData.devicesVlanData[0]['devices'];
      const deviceVlanItem = _.filter(vlanViewData, item => {
        return item.id === d.id;
      });
      if (deviceVlanItem !== undefined && deviceVlanItem[0] !== undefined) {
        if (deviceVlanItem[0].managementVLAN) {
          return this.mxUtils.removeHexAlpha(vlanViewColor.managementVlanColor);
        }
      }
    }
  }

  updateSelection(d: DeviceUnit): string {
    const normal = 'normal';

    if (!this.mxData.showSeverity) return normal;

    switch (d.severity) {
      case Severity.EVENT_SEVERITY_WARNING:
        return 'warning-color normal';
      case Severity.EVENT_SEVERITY_CRITICAL:
        return 'critical-color normal';
      default:
        return this.mxData.showInfoCircle ? 'info-color normal' : normal;
    }
  }

  updateDeviceText(id?: number): void {
    const selection: d3.Selection<any, any, any, any> = id !== undefined ? this.mxD3.d3GForeground : this.deviceElement;
    const hasBottomLabel = this.mxData.deviceAppearanceData.bottomLabelType !== BottomLabel.NONE;
    const deviceTextClass = id !== undefined ? `g#device_${id} text#device-text` : 'text#device-text';
    const labelTextClass = id !== undefined ? `g#device_${id} text#device-label-text` : 'text#device-label-text';

    selection
      .select<SVGGElement>(deviceTextClass)
      .text((d: DeviceUnit) => d.ip)
      .attr('class', d => this.updateTextColor(d))
      .attr('opacity', this.updateOpacity);

    if (hasBottomLabel) {
      selection
        .select<SVGGElement>(labelTextClass)
        .text(d => this.updateBottomLabelText(d))
        .attr('class', d => this.updateTextColor(d))
        .attr('opacity', this.updateOpacity);
    }
  }

  updateBottomLabelText(d: DeviceUnit): string {
    const defaultText = '';

    switch (this.mxData.deviceAppearanceData.bottomLabelType) {
      case BottomLabel.sysLOCATION:
        return d.location;
      case BottomLabel.ALIAS:
        return d.alias;
      case BottomLabel.MODEL_NAME:
        return d.model;
      case BottomLabel.MAC_ADDRESS:
        return d.mac;
      default:
        return defaultText;
    }
  }

  updateTextColor(d: DeviceUnit): string {
    const defaultColor = 'info-text-color select-none';

    if (!this.mxData.showSeverity) return defaultColor;

    switch (d.severity) {
      case Severity.EVENT_SEVERITY_WARNING:
        return 'warning-text-color select-none';
      case Severity.EVENT_SEVERITY_CRITICAL:
        return 'critical-text-color select-none';
      default:
        return defaultColor;
    }
  }

  updateDeviceImage(id?: number): void {
    const selection: d3.Selection<any, any, any, any> =
      id !== undefined
        ? this.mxD3.d3GForeground.select<SVGGElement>(`g#device_${id} image`)
        : this.deviceElement.select('image');

    selection
      .attr('xlink:href', (d: DeviceUnit) => this.getDeviceIconUrl(d))
      .attr('x', d => this.updateX(d))
      .attr('width', d => this.updateWidth(d))
      .attr('opacity', this.updateOpacity);
  }

  getDeviceIconUrl(device: DeviceUnit): string | null {
    if (!device.icon_url) return null;

    const [, path] = device.icon_url.split('/');
    return `${this.mxData.hostUrl}${this.mxData.hostImageUrl}${path}`;
  }

  updateX(d: DeviceUnit): number {
    return this.isLargeWidthIcon(d.icon_url) ? -50 : -25;
  }

  updateWidth(d: DeviceUnit): number {
    return this.isLargeWidthIcon(d.icon_url) ? 100 : 50;
  }

  isLargeWidthIcon(iconUrl: string): boolean {
    const iconName = iconUrl.split('/').pop();

    if (largeWidthIconsExcpetion.some(exception => iconName.startsWith(exception))) {
      return false;
    }
    return largeWidthIcons.some(icon => iconName.startsWith(icon));
  }

  updateOpacity(d: DeviceUnit): number {
    return d.opacity ?? 1;
  }

  updateDeviceTrapParseStatusIcon(id: number): void {
    const selection =
      id === undefined
        ? this.mxD3.d3GForeground.selectAll<SVGGElement, any>('g.device')
        : this.mxD3.d3GForeground.select<SVGGElement>('g#device_' + id);

    selection.each(data => {
      const device = this.mxD3.d3GForeground.select(`g#device_${data.id}`);
      const deviceTrapIcon = device.selectAll('#trap-icon');
      const trapParseStatus = data.v3_trap_parse_status;
      deviceTrapIcon.remove();

      if (trapParseStatus === 1) {
        const deviceIcon = device.append('g').attr('id', 'trap-icon').attr('transform', 'translate(-47,-3)');

        deviceIcon
          .append('path')
          .attr('d', 'M10 10h3v10H10z')
          // avoid transparent
          .attr('fill', '#FFF');

        deviceIcon
          .append('path')
          .attr('d', 'M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z')
          .attr('class', 'warning-text-color select-none')
          .classed('trap-path', true);
      }
    });
  }

  updateDeviceRole(id?: number, modbusEnabled?: boolean): void {
    const selection =
      id === undefined
        ? this.mxD3.d3GForeground.selectAll<SVGGElement, any>('g.device')
        : this.mxD3.d3GForeground.select<SVGGElement>(`g#device_${id}`);

    selection.each((device, i, elements) => {
      this.removeExistingRoleTexts(device.id);

      const roleTexts = this.getAllRoleTexts(device, modbusEnabled);

      roleTexts.forEach((role, index) => {
        const roleTextPosition = deviceRoleTextPosition + index * deviceRoleTextOffset;
        this.createDeviceRoleText(device, elements[i], `device-role-text-${index + 1}`, roleTextPosition, role);
      });
    });
  }

  removeExistingRoleTexts(deviceId: number): void {
    this.mxD3.d3GForeground.select(`g#device_${deviceId}`).selectAll(`text.device-role-text`).remove();
  }

  getAllRoleTexts(device: any, modbusEnabled?: boolean): string[] {
    return [
      ...this.getRedundancyRole(device.redundancy),
      ...this.getRouterRole(device.router_role),
      ...this.getWirelessRole(device.wireless_role),
      ...this.getModbusRole(device.modbus_info, false),
    ];
  }

  getRedundancyRole(redundancy: number | undefined): string[] {
    if (!redundancy) return [];

    return Object.keys(deviceRedundancy)
      .filter(role => (redundancy & deviceRedundancy[role]) > 0)
      .map(role => deviceRedundancyText[deviceRedundancy[role]]);
  }

  getRouterRole(role: number | undefined): string[] {
    const routerRoleConfig = {
      [routerRole.firewall]: [routerRoleName.firewall],
      [routerRole.vpn]: [routerRoleName.vpn],
      [routerRole.nat]: [routerRoleName.nat],
      [routerRole.firewallAndVpn]: [routerRoleName.firewall, routerRoleName.vpn],
      [routerRole.firewallAndNat]: [routerRoleName.firewall, routerRoleName.nat],
      [routerRole.vpnAndNat]: [routerRoleName.vpn, routerRoleName.nat],
      [routerRole.firewallAndVpnAndNat]: [routerRoleName.firewall, routerRoleName.vpn, routerRoleName.nat],
    };
    return (role && routerRoleConfig[role]) || [];
  }

  getWirelessRole(role: number | undefined): string[] {
    const wirelessRoles = {
      [wirelessRole.ap]: wirelessRoleString.ap,
      [wirelessRole.client]: wirelessRoleString.client,
      [wirelessRole.mesh]: wirelessRoleString.mesh,
    };
    return role && wirelessRoles[role] ? [wirelessRoles[role]] : [];
  }

  getModbusRole(modbusInfo: any, modbusEnabled?: boolean): string[] {
    return modbusEnabled && modbusInfo?.modbus_enabled ? ['Modbus'] : [];
  }

  createDeviceRoleText(device: DeviceUnit, element: SVGGElement, id: string, position: number, name: string): void {
    this.deviceRoleElement = this.mxD3.d3GForeground.select(`g#device_${device.id}`).selectAll(`text#${id}`);
    const isEmpty = this.deviceRoleElement.empty();
    const showRole = this.mxData.showRole ? 'visible' : 'hidden';
    const textSize = this.mxData.topologyTextSize;

    if (isEmpty) {
      this.deviceRoleElement = this.mxD3.d3
        .select(element)
        .append('text')
        .attr('class', 'select-none device-role-text')
        .attr('text-anchor', 'middle')
        .attr('x', 0)
        .attr('opacity', this.updateOpacity)
        .style('visibility', showRole);
    }

    this.deviceRoleElement
      .attr('font-size', textSize)
      .attr('id', id)
      .attr('y', position)
      .text(name)
      .attr('opacity', this.updateOpacity)
      .style('visibility', showRole);
  }

  updateTooltip(id?: number): void {
    const selection =
      id === undefined
        ? this.mxD3.d3GForeground.selectAll<SVGGElement, any>('g.device')
        : this.mxD3.d3GForeground.select<SVGGElement>(`g#device_${id}`);

    selection.each(data => {
      if (data.tooltip) {
        this.mxUtils.updateTooltip(`device-${data.id}`, data.tooltip, data.x, data.y);
      }
    });
  }

  updateDeviceDistance(action: number, distance, r: number, id?): void {
    // this.mxD3.d3Svg.select('defs').remove();
    if (action === Action.NEW) {
      const defs = this.mxD3.d3Svg.append('defs');
      this.deviceElement.each(d => {
        const gradient = defs.append('radialGradient').attr('id', `radial-gradient-${d.id}`);
        gradient.append('stop').attr('id', 'red').attr('offset', '0%').attr('stop-color', 'red');
        gradient
          .append('stop')
          .attr('id', 'yellow')
          .attr('offset', distance.high / distance.low)
          .attr('stop-color', 'yellow')
          .attr('stop-opacity', '0.3');
        gradient
          .append('stop')
          .attr('id', 'aqua')
          .attr('offset', distance.medium / distance.low)
          .attr('stop-color', 'aqua')
          .attr('stop-opacity', '0.1');
      });
    }
    if (id !== undefined) {
      this.mxD3.heatmapCircle.select<SVGGElement>(`circle#distance-circle-${id}`).attr('r', r);
      this.mxD3.d3Svg
        .select(`defs radialGradient#radial-gradient-${id} stop#yellow`)
        .attr('offset', distance.high / distance.low);
      this.mxD3.d3Svg
        .select(`defs radialGradient#radial-gradient-${id} stop#aqua`)
        .attr('offset', distance.medium / distance.low);
    } else {
      this.mxD3.heatmapCircle.select('circle.distance-circle').attr('r', r);
      this.mxD3.heatmapCircle.each(d => {
        this.mxD3.d3Svg
          .select(`defs radialGradient#radial-gradient-${d.id} stop#yellow`)
          .attr('offset', distance.high / distance.low);
        this.mxD3.d3Svg
          .select(`defs radialGradient#radial-gradient-${d.id} stop#aqua`)
          .attr('offset', distance.medium / distance.low);
      });
    }
    // The bigger circle is in lower layer
    // this.mxD3.d3GForeground.selectAll('g.device').sort((a: any, b: any) => {
    //   const r_a = +this.mxD3.d3GForeground.select<SVGGElement>(`g#device_${a.id} circle.distance-circle`).attr('r');
    //   const r_b = +this.mxD3.d3GForeground.select<SVGGElement>(`g#device_${b.id} circle.distance-circle`).attr('r');
    //   return r_b - r_a;
    // }).order();
  }

  subscribeDeviceUpdatedData(): void {
    // register eventbus to get updated device data, when get updated device data then put them to all device data set
    this.mxData.deviceDataSubscribe = this.topologyService.updateDeviceData().subscribe(updateDeviceData => {
      if (updateDeviceData.deviceData[0]?.modbus_info) {
        const device = updateDeviceData.deviceData[0];
        this.updateDeviceRole(device.id, device.modbus_info.modbus_enabled);
      }
      if (!this.mxData.autoLayout) {
        let siteId;
        let groupId;
        if (updateDeviceData.deviceData.length !== 0) {
          siteId = updateDeviceData.deviceData[0].site_id;
          groupId = updateDeviceData.deviceData[0].group;
        } else {
          siteId = updateDeviceData.triggerMessage.data.key;
        }
        // Only check device data of current site
        if (
          (updateDeviceData.triggerMessage.datatype === DataType.DEVICE ||
            updateDeviceData.triggerMessage.datatype === DataType.MESSAGE) &&
          this.mxData.oldSiteId === siteId
        ) {
          if (updateDeviceData.triggerMessage.action === Action.NEW && this.mxData.oldGroupId === groupId) {
            if (this.mxData.getDebugFlag()) {
              console.log('subscribeDeviceUpdatedData add new device =' + updateDeviceData.deviceData[0].id);
            }
            // Only check device data of current group
            const device = updateDeviceData.deviceData[0];
            this.addDevice(device);
          } else if (updateDeviceData.triggerMessage.action === Action.UPDATE && this.mxData.oldGroupId === groupId) {
            const device = updateDeviceData.deviceData[0];

            // Check if the device has been added to an ioPAC device group
            const isPartOfIopacGroup = this.isDevicePartOfIopacGroup(device);

            // If the device is now part of an ioPAC group, remove it from the topology
            if (isPartOfIopacGroup) {
              const deviceIndex = this.mxData.nodes.findIndex(o => o.type === 'device' && o.id === device.id);
              if (deviceIndex !== -1) {
                // Remove the device from the nodes array
                this.mxData.nodes = _.reject(this.mxData.nodes, o => {
                  return o.type === 'device' && o.id === device.id;
                });
                this.updateDeviceElement(Action.REMOVE, this.viewMode, this.mxData.securityViewFilterName, false);

                // Update the device in allDevicesData
                const allDevicesIndex = this.mxData.allDevicesData.findIndex(o => o.id === device.id);
                if (allDevicesIndex !== -1) {
                  this.mxData.allDevicesData[allDevicesIndex] = device;
                }

                // We'll redraw the topology after processing all devices
                // Store a flag to indicate that we need to redraw
                this.needsRedraw = true;
                return;
              }
            }

            // Only check device data of current group
            const updateTime = new Date().getTime();
            // Prevent quick update device data
            if (updateTime - this.mxD3.prevDragEndTime > 100) {
              const index = _.findIndex(this.mxData.nodes, o => o.type === 'device' && o.id === device.id);
              if (index !== -1) {
                if (
                  updateDeviceData.triggerMessage.datatype === DataType.AVAILABILITY &&
                  updateDeviceData.triggerMessage.data.key3 !== undefined &&
                  this.mxData.nodes[index].availability !== updateDeviceData.triggerMessage.data.key3
                ) {
                  this.mxData.nodes[index].availability = updateDeviceData.triggerMessage.data.key3;
                  device.availability = updateDeviceData.triggerMessage.data.key3;
                  if (
                    device.device_components.Availability !== undefined &&
                    device.device_components.Availability.status !== undefined
                  ) {
                    device.device_components.Availability.status = updateDeviceData.triggerMessage.data.key3;
                  }
                  this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.UPDATE_DEVICE_DATA, device));
                } else {
                  let updateLink = false;
                  // Update link when position changed
                  const newDevice: DeviceUnit = this.mxUtils.cloneObject(updateDeviceData.deviceData[0]);
                  if (
                    !this.mxData.dynamicWirelessClientPosition &&
                    ((newDevice.x !== undefined &&
                      newDevice.y !== undefined &&
                      this.mxData.nodes[index].x !== newDevice.x) ||
                      this.mxData.nodes[index].y !== newDevice.y)
                  ) {
                    updateLink = true;
                    this.mxPrpHsr.getDeviceRectId(newDevice.id, newDevice.x, newDevice.y);
                  }
                  // Update device when certain property change
                  //ANCHOR - Subscribe WirelessLinkData
                  if (
                    updateLink ||
                    this.mxData.nodes[index].id !== newDevice.id ||
                    this.mxData.nodes[index].sysobjid !== newDevice.sysobjid ||
                    this.mxData.nodes[index].icon_url !== newDevice.icon_url ||
                    this.mxData.nodes[index].mac !== newDevice.mac ||
                    this.mxData.nodes[index].model !== newDevice.model ||
                    this.mxData.nodes[index].location !== newDevice.location ||
                    this.mxData.nodes[index].alias !== newDevice.alias ||
                    this.mxData.nodes[index].severity !== newDevice.severity ||
                    this.mxData.nodes[index].v3_trap_parse_status !== newDevice.v3_trap_parse_status ||
                    this.mxData.nodes[index].ip !== newDevice.ip ||
                    this.mxData.nodes[index].redundancy !== newDevice.redundancy ||
                    this.mxData.nodes[index].device_prphsr_role !== newDevice.device_prphsr_role ||
                    this.mxData.nodes[index].router_role !== newDevice.router_role ||
                    this.mxData.nodes[index]?.wireless_role !== newDevice.wireless_role ||
                    this.mxData.nodes[index]?.mms_info?.model !== newDevice?.mms_info?.model ||
                    this.mxData.nodes[index]?.mms_info?.revision !== newDevice?.mms_info?.revision ||
                    this.mxData.nodes[index]?.mms_info?.vendor !== newDevice?.mms_info?.vendor
                  ) {
                    this.mxUtils.updateDeviceProp(this.mxData.nodes[index], newDevice);
                    this.updateDeviceStyle(true, this.mxData.nodes[index].id);
                  }
                  // Update link position if necessary
                  if (updateLink && this.mxData.hasLinksData()) {
                    this.updateLinkPosition(newDevice);
                  }
                  // Update SNR of dynamic link if necessary
                  if (
                    newDevice.device_components &&
                    newDevice.wireless_role !== wirelessRole.mesh &&
                    newDevice.device_components['wlanSignal'] &&
                    newDevice.device_components['wirelessStatusNoiseLevel'] &&
                    newDevice.device_components.wlanChannel &&
                    newDevice.device_components.wlanChannel.length > 0
                  ) {
                    _.forEach(this.mxData.linksData, (link: LinkUnit) => {
                      if (link.id > 10000000 && newDevice.id === link.clientId) {
                        const snr = +this.mxUtils.getDeviceProp(newDevice.device_components['wirelessStatusSNR-A']);
                        if (link.snr !== snr || link.channel !== +newDevice.device_components.wlanChannel[0].status) {
                          // Update SNR and channel to link
                          link.snr = snr;
                          link.channel = +newDevice.device_components.wlanChannel[0].status;
                          this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.UPDATE_LINK, link));
                          if (this.mxData.getWirelessDebugFlag()) {
                            const wirelessDevice = this.mxData.getWirelessRoleFromLink(link);
                            console.log(
                              '%c[' +
                                this.mxData.getTimeString() +
                                '] AP: ' +
                                wirelessDevice.ap.ip +
                                ' | Client: ' +
                                wirelessDevice.client.ip +
                                ' Update SNR: ' +
                                link.snr,
                              'background: #D6D6D4; color: #000000'
                            );
                          }
                          const wirelessDevice = this.mxData.getWirelessRoleFromLink(link);
                          ServerConfigurationService.log(
                            '[' +
                              this.mxData.getTimeString() +
                              '] AP: ' +
                              wirelessDevice.ap.ip +
                              ' | Client: ' +
                              wirelessDevice.client.ip +
                              ' Update SNR: ' +
                              link.snr
                          );
                        }
                      }
                    });
                  }
                  if (
                    newDevice.device_components &&
                    newDevice.wireless_role === wirelessRole.mesh &&
                    newDevice.device_components['wlanSignal'] &&
                    (newDevice.device_components['wirelessStatusSNR-A'] ||
                      (newDevice.device_components.wlanChannel && newDevice.device_components.wlanChannel.length > 0))
                  ) {
                    _.forEach(this.mxData.linksData, (link: LinkUnit) => {
                      if (link.id > 10000000 && newDevice.id === link.from_device) {
                        const meshRFBand = newDevice.device_components['meshRFBand'];
                        const wlanRFBand = newDevice.device_components['wlanRFBand'].find(
                          band => band.index === meshRFBand.status
                        );
                        const wlanChannel = newDevice.device_components.wlanChannel.find(
                          channel => channel.index === wlanRFBand.status
                        );
                        const snr = +this.mxUtils.getDeviceProp(newDevice.device_components['wirelessStatusSNR-A']);
                        if (link.snr !== snr || link.channel !== +wlanChannel.status) {
                          // Update SNR and channel to link
                          link.snr = snr;
                          link.channel = +wlanChannel.status;
                          this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.UPDATE_LINK, link));
                          if (this.mxData.getWirelessDebugFlag()) {
                            const wirelessDevice = this.mxData.getWirelessRoleFromLink(link);
                            console.log(
                              '%c[' +
                                this.mxData.getTimeString() +
                                '] AP: ' +
                                wirelessDevice.ap.ip +
                                ' | Client: ' +
                                wirelessDevice.client.ip +
                                ' Update SNR: ' +
                                link.snr,
                              'background: #D6D6D4; color: #000000'
                            );
                          }
                          const wirelessDevice = this.mxData.getWirelessRoleFromLink(link);
                          ServerConfigurationService.log(
                            '[' +
                              this.mxData.getTimeString() +
                              '] AP: ' +
                              wirelessDevice.ap.ip +
                              ' | Client: ' +
                              wirelessDevice.client.ip +
                              ' Update SNR: ' +
                              link.snr
                          );
                        }
                      }
                    });
                  }
                }
              }
            }
          } else if (updateDeviceData.triggerMessage.action === Action.REMOVE) {
            if (this.mxData.getDebugFlag()) {
              console.log('subscribeDeviceUpdatedData remove device');
            }
            if (updateDeviceData.triggerMessage.data.key3 !== undefined) {
              // key3 代表是 device 有 virtual_id 時，要刪除虛擬節點
              this.mxData.nodes = _.reject(this.mxData.nodes, o => {
                return o.type === 'neighbor_device' && o.id === updateDeviceData.triggerMessage.data.key2;
              });
              this.mxNeighborDevice.updateNeighborDevicesElement(Action.REMOVE);
            } else {
              this.removeDevice(updateDeviceData.triggerMessage.data.key2);
            }
          }
        }
      }
    });
  }

  addDevice(device): void {
    if (this.mxData.getDebugFlag()) {
      console.log('addDevice = ' + device.id);
    }

    // Check if this device is part of an ioPAC device group
    const isPartOfIopacGroup = this.isDevicePartOfIopacGroup(device);
    if (isPartOfIopacGroup) {
      // If the device is part of an ioPAC device group, add it to allDevicesData but don't display it
      let index = this.mxData.allDevicesData.findIndex(o => o.id === device.id);
      if (index === -1) {
        this.mxData.allDevicesData.push(device);
      }
      return;
    }

    device.type = 'device';
    device.forceId = 'device_' + device.id;
    // Remove duplicate neighbor_device from nodes
    let index = this.mxData.nodes.findIndex(o => {
      return o.type === 'neighbor_device' && o.id === device.id;
    });
    if (index !== -1) {
      this.mxData.nodes = _.reject(this.mxData.nodes, o => {
        return o.type === 'neighbor_device' && o.id === device.id;
      });
      this.mxNeighborDevice.updateNeighborDevicesElement(Action.REMOVE);
    }
    // Detect device change to current group
    // Remove device from previous group in search bar
    index = this.mxData.allDevicesData.findIndex(o => o.id === device.id);
    if (index === -1) {
      this.mxData.allDevicesData.push(device);
    } else {
      const removePara = {
        action: Action.REMOVE,
        device: this.mxData.allDevicesData[index],
      };
      this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.UPDATE_SEARCH, removePara));
    }
    // Push to nodes
    index = this.mxData.nodes.findIndex(o => o.type === 'device' && o.id === device.id);
    if (index === -1) {
      const newDevice = this.mxUtils.cloneObject(device);
      this.mxData.nodes.push(newDevice);
    }
    this.updateDeviceElement(Action.NEW, this.viewMode, this.mxData.securityViewFilterName, true, device.id);
    this.mxUtils.resetForegroundSize();
    // Update search bar
    const para = {
      action: Action.NEW,
      device,
    };
    this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.UPDATE_SEARCH, para));
  }

  /**
   * Checks if a device is part of an ioPAC device group
   * @param device The device to check
   * @returns True if the device is part of an ioPAC device group, false otherwise
   */
  private isDevicePartOfIopacGroup(device: DeviceUnit): boolean {
    if (!device.group) return false;

    // Check if the device's group is an ioPAC device group
    const group = this.mxData.groupsData.find(g => g.id === device.group);
    return group?.device_group === true;
  }

  removeDevice(deviceId): void {
    if (this.mxData.getDebugFlag()) {
      console.log('removeDevice deviceId = ' + deviceId);
    }
    // Remove device from search bar
    const device = _.filter(this.mxData.allDevicesData, o => {
      return o.id === deviceId;
    });
    if (device !== undefined && device.length > 0) {
      const para = {
        action: Action.REMOVE,
        device: device[0],
      };
      this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.UPDATE_SEARCH, para));
    }
    // Remove previous selected device
    this.mxData.currentSelectedDevices = [];
    this.mxData.allDevicesData = _.reject(this.mxData.allDevicesData, o => {
      return o.id === deviceId;
    });
    this.mxData.nodes = _.reject(this.mxData.nodes, o => {
      return o.type === 'device' && o.id === deviceId;
    });
    // Delete repetitiveElbowLinks device
    delete this.mxData.repetitiveElbowLinks[deviceId];
    this.updateDeviceElement(Action.REMOVE, this.viewMode, this.mxData.securityViewFilterName, true);
    // Remove device tooltip
    this.mxUtils.removeTooltip('device-' + deviceId);
    this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.CLOSE_SIDE_BAR, null));
    // Send not selected data to parent
    this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.OBJECT_NOT_SELECTED, null));
    this.mxUtils.resetForegroundSize();
  }

  updateLinkPosition(newDevice: DeviceUnit): void {
    // Update link position if necessary
    if (this.mxData.hasLinksData()) {
      const updateLinks: LinkUnit[] = _.filter(this.mxData.linksData, o => {
        return (
          (o.from_device === newDevice.id && o.from_port !== 0) || (o.to_device === newDevice.id && o.to_port !== 0)
        );
      });
      if (this.mxData.topologyStyle[0].topology_style === topologyStyle.directed) {
        updateLinks.forEach((updateLink: LinkUnit) => {
          this.mxD3.d3Link.select('path#link_' + updateLink.id).attr('d', (link: LinkUnit) => {
            return this.mxDirectedLink.drawDirectedLine(link);
          });
        });
        this.mxDirectedLink.updateDirectedLinePortText(updateLinks);
      } else {
        updateLinks.forEach((updateLink: LinkUnit) => {
          this.mxD3.d3Link.select('path#link_' + updateLink.id).attr('d', (data: any) => {
            this.mxElbowLink.findElbowRepetitiveLink(data, Action.UPDATE);
            return this.mxElbowLink.drawElbowLine(data, Action.UPDATE);
          });
        });
        this.mxElbowLink.updateElbowLinePortText(updateLinks);
      }
      // 判斷是否要重新計算畫布大小
      if (
        newDevice.x + circleRadius.node >= this.mxData.foregroundWidth ||
        newDevice.y + circleRadius.node >= this.mxData.foregroundHeight
      ) {
        this.mxUtils.resetForegroundSize();
      }
    }
  }

  updateDevice(updateStyle: boolean, device: DeviceUnit): void {
    const index = _.findIndex(this.mxData.nodes, o => o.id === device.id && o.type === 'device');
    if (index !== -1) {
      this.mxUtils.updateDeviceProp(this.mxData.nodes[index], device);
      this.updateDeviceStyle(updateStyle, device.id);
      // Update link position if necessary
      if (this.mxData.hasLinksData()) {
        this.updateLinkPosition(device);
      }
    }
  }

  calcWirelessDistance(info: any, rxSensitivity: number): number {
    // const freq = 2412; // or 5240
    const FsValue = 20 * Math.log10(info.freq);
    // const maxTxPower = 18;
    // const Tx_cable_loss = 0;
    // const Tx_antenna_gain = 2;
    // const sr = 6;
    // const rxSensitivity = -65;
    // const Rx_antenna_gain = 2;
    // const Rx_cable_loss = 0;
    // const freeSpaceConst = -27.55; // for meters, km: 32.44;
    const powerValue =
      (info.maxTxPower -
        info.tx_cable_loss +
        info.tx_antenna_gain -
        info.reserved_safety_factor -
        rxSensitivity + // + -rxSensitivity
        info.rx_antenna_gain -
        info.rx_cable_loss -
        FsValue -
        info.freeSpaceConst) /
      20;
    const distance = Math.pow(10, powerValue);
    return Math.round(distance);
  }

  subscribeGooseUpdatedData(): void {
    this.mxData.gooseDataSubscribe = this.topologyService.updateGooseData().subscribe(updateGooseData => {
      const gooseInfo = updateGooseData.data.key;
      const gooseInfoIndex: number = _.findIndex(this.mxData.goose, (goose: IedDeviceUnit) => {
        return goose.device_id === gooseInfo.device_id;
      });
      if (gooseInfoIndex !== -1) {
        this.mxData.goose[gooseInfoIndex].device_id = gooseInfo.device_id;
        // Check if there are any GOOSE status changed on devices
        let gooseStatusChange = false;
        if (this.mxData.goose[gooseInfoIndex].status !== gooseInfo.status) {
          gooseStatusChange = true;
          if (gooseInfo.status === 'timeout' || gooseInfo.status === 'tampered') {
            clearTimeout(this.gooseClearStatusTimeout);
            if (this.mxData.getPowerDebugFlag()) {
              console.log(
                '%c[' +
                  this.mxData.getTimeString() +
                  '] IED IP: ' +
                  this.mxUtils.getNode(gooseInfo.device_id).ip +
                  ' ,GOOSE Status:' +
                  gooseInfo.status,
                'background: #D6D6D4; color: #000000'
              );
            }
            ServerConfigurationService.log(
              '[' +
                this.mxData.getTimeString() +
                '] IED IP: ' +
                this.mxUtils.getNode(gooseInfo.device_id).ip +
                ' ,GOOSE Status:' +
                gooseInfo.status
            );
            this.mxGoose.updateDeviceGooseStatusIcon(this.mxData.goose[gooseInfoIndex].device_id, gooseInfo.status);
          } else {
            if (this.mxData.getPowerDebugFlag()) {
              console.log(
                '%c[' +
                  this.mxData.getTimeString() +
                  '] IED IP: ' +
                  this.mxUtils.getNode(gooseInfo.device_id).ip +
                  ' ,GOOSE Status:' +
                  gooseInfo.status,
                'background: #D6D6D4; color: #000000'
              );
            }
            ServerConfigurationService.log(
              '[' +
                this.mxData.getTimeString() +
                '] IED IP: ' +
                this.mxUtils.getNode(gooseInfo.device_id).ip +
                ' ,GOOSE Status:' +
                gooseInfo.status
            );
            this.mxGoose.updateDeviceGooseStatusIcon(this.mxData.goose[gooseInfoIndex].device_id, gooseInfo.status);
          }
        }
        // 先比對完後再 Assign GOOSE status
        this.mxData.goose[gooseInfoIndex].status = gooseInfo.status;
        _.forEach(gooseInfo.published_gms, (goosePublisher: GoosePublisher, goosePublisherIndex: number) => {
          this.mxData.goose[gooseInfoIndex].published_gms[goosePublisherIndex].app_id = goosePublisher.app_id;
          this.mxData.goose[gooseInfoIndex].published_gms[goosePublisherIndex].cb_name = goosePublisher.cb_name;
          this.mxData.goose[gooseInfoIndex].published_gms[goosePublisherIndex].mac = goosePublisher.mac;
          this.mxData.goose[gooseInfoIndex].published_gms[goosePublisherIndex].status = goosePublisher.status;
          this.mxData.goose[gooseInfoIndex].published_gms[goosePublisherIndex].vlan_id = goosePublisher.vlan_id;
        });
        _.forEach(gooseInfo.subscribed_gms, (gooseSubscriber: GooseSubscriber, gooseSubscriberIndex: number) => {
          this.mxData.goose[gooseInfoIndex].subscribed_gms[gooseSubscriberIndex].ied_name = gooseSubscriber.ied_name;
          this.mxData.goose[gooseInfoIndex].subscribed_gms[gooseSubscriberIndex].cb_name = gooseSubscriber.cb_name;
        });
        // 如果 GOOSE panel 是開啟的，則要更新 GOOSE 的狀態
        if (
          this.mxData.selectedGooseDeviceData !== undefined &&
          (this.mxData.selectedGooseDeviceData.published_gms.length > 0 ||
            this.mxData.selectedGooseDeviceData.subscribed_gms.length > 0)
        ) {
          this.mxData.selectedGooseDeviceData = _.find(
            this.mxData.goose,
            item => item.device_id === this.mxData.selectedGooseDeviceData.device_id
          );
          this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.MARK_FOR_CHECK, null));
        }
        // 更新的 GOOSE 狀態並顯示 icon 在 GOOSE List Button 的右上方
        if (gooseStatusChange) {
          this.mxGoose.updateAllGooseStatus();
          this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.MARK_FOR_CHECK, null));
        }
      }
    });
  }

  isUpdateHintPanel(device?: DeviceUnit): string | boolean {
    return (
      !device ||
      (device && device.ied_name) ||
      (device &&
        !device.ied_name &&
        this.mxD3.d3GForeground
          .select('g#device_' + device.id)
          .select('.gooseStatusIcon')
          .size() === 0)
    );
  }

  updateHintPanelPos(x?: number, y?: number): void {
    this.mxData.serialPortPanelPos = {
      x: ((x !== undefined ? x : this.mxData.currentSelectedDevices[0].x) + 60) * this.mxD3.zoomValue,
      y:
        (y !== undefined ? y : this.mxData.currentSelectedDevices[0].y) * this.mxD3.zoomValue -
        this.mxData.foregroundHeight,
    };
    this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.MARK_FOR_CHECK, null));
  }

  hideDeviceHint(device?: DeviceUnit): void {
    const element = document.getElementById('nport-panel');
    if (!this.isUpdateHintPanel(device) || !element) return;
    if (element.style.display === 'block') {
      element.style.display = 'none';
    }
  }

  showDeviceHint(device: DeviceUnit): void {
    const hasDeviceHint = this.mxData.serialPortStatus?.some(triggerDevice => triggerDevice.id === device.id) ?? false;
    const element = document.getElementById('nport-panel');
    if (!this.isUpdateHintPanel(device) || !hasDeviceHint || !element) return;

    element.style.display = 'block';
    this.updateHintPanelPos(device.x, device.y);
  }

  subscribeSerialPortData(): void {
    this.mxData.serialPortDataSubscribe = this.topologyService
      .updateSerialPortData()
      .subscribe((updateSerialPortData: SerialPortStatusResult) => {
        if (!this.hasValidTriggers(updateSerialPortData)) {
          this.removeSerialPortStatusIcon([updateSerialPortData.id]);
          return;
        }
        this.appendSerialPortStatusIcon([updateSerialPortData.id]);
      });
  }

  appendSerialPortStatusIcon(deviceId: number[]): void {
    if (deviceId.length <= 0) return;

    deviceId.forEach(id => {
      const foreGroundDevice = this.mxD3.d3GForeground.select(`g#device_${id}`);
      const serialPortIcon = foreGroundDevice.selectAll('#serial-port-icon');
      serialPortIcon.remove();

      const deviceIcon = foreGroundDevice.append('g').attr('id', 'serial-port-icon');

      deviceIcon
        .append('path')
        .attr('d', 'M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z')
        .attr('class', 'select-none')
        .attr('fill', '#F8D51C')
        .attr('transform', 'translate(30, -50)')
        .classed('trap-path', true);
    });
  }

  removeSerialPortStatusIcon(deviceId: number[]): void {
    if (deviceId.length <= 0) return;

    deviceId.forEach(id => {
      const foreGroundDevice = this.mxD3.d3GForeground.select(`g#device_${id}`);
      const serialPortIcon = foreGroundDevice.selectAll('#serial-port-icon');
      serialPortIcon.remove();
    });
  }

  private hasValidTriggers(data: SerialPortStatusResult): boolean {
    return (
      data.trigger_rx ||
      data.trigger_tx ||
      data.trigger_rx_tx ||
      data.trigger_error_count_frame ||
      data.trigger_error_count_parity ||
      data.trigger_error_count_overrun ||
      data.trigger_error_count_break
    );
  }

  private updateSerialPortStatus(serialPortStatus, newTriggerDevice) {
    const isDuplicate = serialPortStatus
      .map(device => device.result.map(result => result.port))
      .flat()
      .includes(newTriggerDevice.result[0].port);

    return isDuplicate
      ? serialPortStatus.map(device => {
          if (device.id !== newTriggerDevice.id) return device;
          return {
            ...device,
            result: [
              ...device.result.filter(result => result.port !== newTriggerDevice.result[0].port),
              newTriggerDevice.result[0],
            ],
          };
        })
      : [...serialPortStatus, newTriggerDevice];
  }

  getTriggerDevices(serialPortStatus: SerialPortStatus['result']): SerialPortTriggerDevice[] {
    return serialPortStatus.reduce((acc, current) => {
      if (!acc.some(item => item.id === current.id))
        return [...acc, { id: current.id, result: this.mapResults(current) }];

      return acc.map(item => {
        if (item.id !== current.id) return item;
        return {
          id: current.id,
          result: [...item.result, ...this.mapResults(current)],
        };
      });
    }, []);
  }

  filterValidResults(results: SerialPortStatusResult): boolean {
    return (
      results.trigger_rx ||
      results.trigger_tx ||
      results.trigger_rx_tx ||
      results.trigger_error_count_frame ||
      results.trigger_error_count_parity ||
      results.trigger_error_count_overrun ||
      results.trigger_error_count_break
    );
  }

  mapResults(portResult: SerialPortStatusResult): SerialPortTriggerDeviceResult[] {
    return this.mapPortResult(portResult).flat();
  }

  mapPortResult(portResult: SerialPortStatusResult): SerialPortTriggerDeviceResult[] {
    return Object.keys(portResult)
      .filter(key => portResult[key] === true)
      .map(key => this.createTriggerObject(portResult, key));
  }

  createTriggerObject(portResult: SerialPortStatusResult, key: string): SerialPortTriggerDeviceResult {
    const triggerKey = key.replace('trigger_', '');
    return {
      port: portResult.port,
      severity: portResult[`severity_${triggerKey}`],
      triggers: TriggerMapping[key],
      duration: portResult[`duration_${triggerKey}`],
      count: portResult[`threshold_${triggerKey}`] ?? -1,
    };
  }
}
