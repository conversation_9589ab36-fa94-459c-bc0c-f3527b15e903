import * as _ from 'lodash';

import { Action, DataType, Severity } from '../../Service/mx-platform/DataDef/DataTypeDefinitions';
import { GroupUnit } from '../../Service/mx-platform/DataDef/GroupDataDefs';
import { LinkUnit } from '../../Service/mx-platform/DataDef/LinkDataDefs';
import { MxTopologyD3 } from './mx-topology-d3';
import { MxTopologyD3Event } from './mx-topology-d3-event';
import { MxTopologyData } from './mx-topology-data';
import { MxTopologyDirectedLink } from './mx-topology-directed-link';
import { MxTopologyElbowLink } from './mx-topology-elbow-link';
import { MxTopologyEvent, MxTopologyEventType } from './mx-topology-event';
import { MxTopologyLink } from './mx-topology-link';
import { MxTopologyUtils } from './mx-topology-utils';
import { circleRadius } from './mx-topology.constants';
import { MxTopologyService } from './mx-topology.service';

export class MxTopologyGroup {
  public groupElement: any;
  public groupEnterElement: any;
  public groupDataElement: any;
  private textSize: string;

  constructor(
    private topologyService: MxTopologyService,
    private mxD3: MxTopologyD3,
    private mxD3Event: MxTopologyD3Event,
    private mxLink: MxTopologyLink,
    private mxDirectedLink: MxTopologyDirectedLink,
    private mxElbowLink: MxTopologyElbowLink,
    private mxData: MxTopologyData,
    private mxUtils: MxTopologyUtils
  ) {}

  updateGroupElement(action: number): void {
    this.textSize = this.mxData.topologyTextSize;
    const nodes = this.mxData.nodes.filter(node => node.type === 'group');
    this.groupDataElement = this.mxD3.d3GForeground.selectAll<SVGGElement, any>('g.group').data(nodes, d => d.id);

    if (action === Action.REMOVE) {
      this.groupDataElement.exit().remove();
      return;
    }

    if (action === Action.NEW) {
      this.groupEnterElement = this.groupDataElement
        .enter()
        .append('g')
        .attr('class', 'node group')
        .call(
          this.mxD3.d3
            .drag<SVGGElement, GroupUnit>()
            .on('start', d => this.mxD3Event.dragStarted(d))
            .on('drag', d => this.mxD3Event.dragged(d))
            .on('end', d => this.mxD3Event.dragEnded(d))
        )
        .on('click', d => this.mxD3Event.onClick(d));
      // Create group selection
      this.groupEnterElement.append('circle').attr('stroke-width', '4px').attr('cx', 0).attr('cy', 0);
      // Create the image for each block
      this.groupEnterElement.append('image').attr('width', 80).attr('height', 80).attr('x', -40).attr('y', -50);
      // Create the text for each block
      this.groupEnterElement
        .append('text')
        .attr('font-size', this.textSize)
        .attr('id', 'group-text')
        .attr('text-anchor', 'middle')
        .attr('x', 0)
        .attr('y', 45);
    }

    this.groupElement = this.groupEnterElement.merge(this.groupDataElement);
    this.updateGroup();
  }

  updateGroup(): void {
    this.groupElement.attr('id', d => `group_${d.id}`).attr('transform', d => `translate(${d.x},${d.y})`);
    this.updateGroupSelection();
    this.updateGroupText();
    this.updateGroupImage();
  }

  updateGroupSelection(): void {
    this.groupElement
      .select('circle')
      .attr('r', circleRadius.node)
      .attr('class', (d: GroupUnit) => this.getGroupSelectionColor(d.severity));
  }

  getGroupSelectionColor(severity: Severity) {
    const defaultColor = this.mxData.showInfoCircle ? 'info-color' : '';

    switch (severity) {
      case Severity.EVENT_SEVERITY_WARNING:
        return 'warning-color';
      case Severity.EVENT_SEVERITY_CRITICAL:
        return 'critical-color';
      default:
        return defaultColor;
    }
  }

  updateGroupText(): void {
    this.groupElement
      .select('text')
      .text((d: GroupUnit) => d.name)
      .attr('class', (d: GroupUnit) => this.getGroupTextColor(d.severity));
  }

  getGroupTextColor(severity: Severity) {
    const defaultColor = 'info-text-color select-none';

    switch (severity) {
      case Severity.EVENT_SEVERITY_WARNING:
        return 'warning-text-color select-none';
      case Severity.EVENT_SEVERITY_CRITICAL:
        return 'critical-text-color select-none';
      default:
        return defaultColor;
    }
  }

  updateGroupImage(): void {
    // Delay get image to ensure image is updated
    setTimeout(() => {
      this.groupElement.select('image').attr('xlink:href', (d: GroupUnit) => {
        if (d.icon_url) {
          return this.mxData.hostUrl + this.mxData.hostImageUrl + d.icon_url + '?t=' + new Date().getTime();
        } else {
          return 'assets/img/devices/group.png';
        }
      });
    }, 500);
  }

  subscribeGroupUpdatedData(): void {
    this.mxData.groupDataSubscribe = this.topologyService.updateGroupData().subscribe(updateGroupData => {
      if (!this.mxData.autoLayout) {
        let siteId;
        const group = updateGroupData.groupData[0];
        if (updateGroupData.groupData.length !== 0) {
          siteId = group.site_id;
        } else {
          siteId = updateGroupData.triggerMessage.data.key;
        }
        if (updateGroupData.triggerMessage.datatype === DataType.GROUP && this.mxData.oldSiteId === siteId) {
          if (updateGroupData.triggerMessage.action === Action.NEW) {
            const index = this.mxData.nodes.findIndex(o => o.type === 'group' && o.id === group.id);
            if (index === -1) {
              group.type = 'group';
              group.forceId = 'group_' + group.id;
              if (group.parent_id.toString() === localStorage.getItem('groupId')) {
                this.mxData.nodes.push(group);
              }
              this.mxData.groupsData.push(group);

              // If this is an ioPAC device group, hide all its member devices
              if (group.device_group) {
                this.hideDevicesInGroup(group.id, true);
              }
            }
            this.updateGroupElement(Action.NEW);
          } else if (updateGroupData.triggerMessage.action === Action.UPDATE) {
            const updateTime = new Date().getTime();
            if (updateTime - this.mxD3.prevDragEndTime > 100) {
              const index = this.mxData.nodes.findIndex(o => o.type === 'group' && o.id === group.id);
              if (index !== -1) {
                const newGroup = this.mxUtils.cloneObject(updateGroupData.groupData[0]);
                let updateLink = false;
                // Update link when position changed
                if (this.mxData.nodes[index].x !== newGroup.x || this.mxData.nodes[index].y !== newGroup.y) {
                  updateLink = true;
                }
                // update group
                newGroup.type = 'group';
                newGroup.forceId = 'group_' + newGroup.id;
                this.mxData.nodes[index] = newGroup;
                this.updateGroupElement(Action.UPDATE);

                // If this is an ioPAC device group, ensure all its member devices are hidden
                if (newGroup.device_group) {
                  this.hideDevicesInGroup(newGroup.id, true);
                }

                // Update link position if necessary
                if (updateLink && this.mxData.hasLinksData()) {
                  const updateLinks: LinkUnit[] = _.filter(this.mxData.linksData, o => {
                    return (
                      (o.from_device === newGroup.id && o.from_port === 0) ||
                      (o.to_device === newGroup.id && o.to_port === 0)
                    );
                  });
                  if (this.mxData.topologyStyle[0].topology_style === 0) {
                    updateLinks.forEach((updateLink: LinkUnit) => {
                      this.mxD3.d3Link.select('path#link_' + updateLink.id).attr('d', (link: LinkUnit) => {
                        return this.mxDirectedLink.drawDirectedLine(link);
                      });
                    });
                    this.mxDirectedLink.updateDirectedLinePortText(updateLinks);
                  } else {
                    updateLinks.forEach((updateLink: LinkUnit) => {
                      this.mxD3.d3Link.select('path#link_' + updateLink.id).attr('d', (data: any) => {
                        this.mxElbowLink.findElbowRepetitiveLink(data, Action.UPDATE);
                        return this.mxElbowLink.drawElbowLine(data, Action.UPDATE);
                      });
                    });
                    this.mxElbowLink.updateElbowLinePortText(updateLinks);
                  }
                  this.mxUtils.resetForegroundSize();
                }
              }
            }
          } else if (updateGroupData.triggerMessage.action === Action.REMOVE) {
            // Remove previous selected group
            this.mxData.currentSelectedGroups = [];
            const groupId = updateGroupData.triggerMessage.data.key2;

            // Check if the removed group was a device_group
            const removedGroup = this.mxData.groupsData.find(g => g.id === groupId);
            const wasDeviceGroup = removedGroup && removedGroup.device_group;

            this.mxData.nodes = _.reject(this.mxData.nodes, o => {
              return o.type === 'group' && o.id === groupId;
            });
            this.mxData.groupsData = _.reject(this.mxData.groupsData, o => {
              return o.id === groupId;
            });
            this.updateGroupElement(Action.REMOVE);
            this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.CLOSE_SIDE_BAR, null));
            // Send not selected data to parent
            this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.OBJECT_NOT_SELECTED, null));

            // If it was a device group, we need to redraw the topology to show the individual devices
            if (wasDeviceGroup) {
              this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.REDRAW, null));
            }
          }
        }
      }
    });
  }

  /**
   * Hides all devices that are part of the specified group
   * @param groupId The ID of the group whose devices should be hidden
   * @param shouldRedraw Whether to redraw the topology after hiding the devices
   */
  private hideDevicesInGroup(groupId: number, shouldRedraw: boolean = true): void {
    // Find all devices that belong to this group
    const devicesToHide = this.mxData.allDevicesData.filter(device => device.group === groupId);

    // Remove these devices from the nodes array to hide them from the topology
    if (devicesToHide.length > 0) {
      // Remove the devices from the nodes array
      this.mxData.nodes = _.reject(this.mxData.nodes, o => {
        return o.type === 'device' && devicesToHide.some(d => d.id === o.id);
      });

      // Update the device elements to reflect the changes, but only if requested
      if (shouldRedraw) {
        this.mxUtils.emitEvent(new MxTopologyEvent(MxTopologyEventType.REDRAW, null));
      }
    }
  }
}
